<template>
  <div class="content-wrap-layout">
    <!-- 分类区 -->
    <contentHeader :title="title" :des="des"/>
    <category :category-name="moduleName" :category-type="2" @category-query="switchCategory"/>
    <page-table
      ref="table"
      :filter-data="{ examStatus }"
      :default-selected-arr="defaultSelectedArr"
      :cache-pattern="true"
      :module-name="moduleName"
      :bank-type="2"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
    />
    <el-drawer
      :visible.sync="detailShow"
      :modal="false"
      size="75%"
      append-to-body
      class="detail-view"
      @close="closeDetail"
    >
      <t-detail v-if="detailShow" />
    </el-drawer>
  </div>
</template>

<script>
import moduleConf from './config'
import category from './category/index'
import pageTable from './table/index'
import contentHeader from '../content-header.vue'
import lodash from 'lodash'
import tDetail from './detail/index.vue'
import moduleMixin from '@/packages/mixins/module_list'
export default {
  components: {
    category,
    pageTable,
    contentHeader,
    tDetail
  },
  mixins: [moduleMixin],
  data() {
    return {
      detailShowOfName: ['detailExamTraining', 'answerRecordTraining'],
      listRouterName: 'TeachingAffairsExamTraining',
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      num: 0,
      examStatus: '',
      title: '教学事务',
      des: '对教学事务一应数据展示分析模块'
    }
  },
  methods: {
    switchCategory: lodash.debounce(function(value) {
      this.examStatus = value
      if (!this.$store.state.cache[this.moduleName]) {
        const obj = {
          data: { searchShow: true },
          key: this.moduleName
        }
        this.$store.commit('SET_CACHE', obj)
      }
      this.$nextTick(() => {
        if (this.num != 0) {
          this.$refs['table']['pageCurrent'] = 1
        } else {
          this.num = this.num + 1
        }
        this.$refs['table'].getList()
      })
    }, 500),
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {}
  }
}
</script>
