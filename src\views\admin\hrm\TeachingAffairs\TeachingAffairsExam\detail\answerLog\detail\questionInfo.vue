<template>
  <div class="detail-question-bank">
    <div class="_paper_search">
      <div class="_paper_search_1">
        总题数
        <span style="font-weight: bold; color: var(--color-600); font-size: 18px; margin-right: 10px;">
          {{ getQuestionNum }}
        </span>
      </div>
      <div class="_paper_search_1">
        试卷总分
        <span style="font-weight: bold; color: var(--color-600); font-size: 18px; margin-right: 10px;">
          {{ paperTotalScore }}
        </span>
      </div>
      <div class="_paper_search_1">
        总成绩
        <span style="font-weight: bold; color: var(--color-600); font-size: 18px;">
          {{ getScore }}
        </span>
      </div>
    </div>
    <el-tabs v-loading="loading" v-model="activeName">
      <el-tab-pane v-if="judgeQuestionList.length" :label="`判断题（${ judgeQuestionList.length }）`" name="emulation">
        <div class="_question_list">
          <div
            v-for="(q, index) in judgeQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.questionType != 10 ? q.content : q.questionName"/></div>
              <div v-if="q.questionUserDetailsScore == 0" class="_question_item_type" style="color: #ff4b40;"> {{ q.questionUserDetailsScore }}分</div>
              <div v-else class="_question_item_type" style="color: #4cbf4b;"> {{ q.questionUserDetailsScore }}分</div>
            </div>

            <div class="_question_option">
              <el-radio-group :value="q.questionUserAnswer" disabled>
                <el-radio label="A">A. 正确</el-radio>
                <el-radio label="B">B. 错误</el-radio>
              </el-radio-group>
            </div>
            <div class="_question_score">
              <div>该题：<span>{{ q.questionScore }}</span> 分</div>
              <div>学员答案：<span>{{ q.questionUserAnswer || '未作答' }}</span></div>
              <div>参考答案：<span>{{ q.questionAnswer }}</span></div>
              <div class="flex-row">题目解析：<span style="display: inline-block;flex-1" v-html="q.questionAnalysis"/></div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="singleQuestionList.length" :label="`单选题（${ singleQuestionList.length }）`" name="theory">
        <!-- 题目列表 -->
        <div class="_question_list">
          <div
            v-for="(q, index) in singleQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.content"/></div>
              <div v-if="q.questionUserDetailsScore == 0" class="_question_item_type" style="color: #ff4b40;"> {{ q.questionUserDetailsScore }}分</div>
              <div v-else class="_question_item_type" style="color: #4cbf4b;"> {{ q.questionUserDetailsScore }}分</div>
            </div>
            <div class="_question_option">
              <el-radio-group
                v-for="(op, i) in JSON.parse(q.questionOptions)"
                :key="op"
                :value="q.questionUserAnswer"
                disabled
              >
                <el-radio :label="optionLabel[i]">{{ optionLabel[i] }}. {{ op }}</el-radio>
              </el-radio-group>
            </div>
            <div class="_question_score">
              <div>该题：<span>{{ q.questionScore }}</span> 分</div>
              <div>学员答案：<span>{{ q.questionUserAnswer || '未作答' }}</span></div>
              <div>参考答案：<span>{{ q.questionAnswer }}</span></div>
              <div>题目解析：<span style="display: inline-block" v-html="q.questionAnalysis"/></div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="multiQuestionList.length" :label="`多选题（${ multiQuestionList.length }）`" name="target">
        <div class="_question_list">
          <div
            v-for="(q, index) in multiQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.content"/></div>
              <div v-if="q.questionUserDetailsScore == 0" class="_question_item_type" style="color: #ff4b40;"> {{ q.questionUserDetailsScore || 0 }}分</div>
              <div v-else class="_question_item_type" style="color: #4cbf4b;"> {{ q.questionUserDetailsScore || 0 }}分</div>
            </div>
            <div>
              <el-checkbox-group :value="q.questionUserAnswer && q.questionUserAnswer.split('')" disabled>
                <div class="_question_option">
                  <el-checkbox
                    v-for="(op, i) in JSON.parse(q.questionOptions)"
                    :key="op"
                    :label="optionLabel[i]"
                  >{{ optionLabel[i] }}. {{ op }}</el-checkbox
                  >
                </div>
              </el-checkbox-group>
            </div>
            <div class="_question_score">
              <div>该题：<span>{{ q.questionScore }}</span> 分</div>
              <div>学员答案：<span>{{ q.questionUserAnswer || '未作答' }}</span></div>
              <div>参考答案：<span>{{ q.questionAnswer }}</span></div>
              <div>题目解析：<span style="display: inline-block" v-html="q.questionAnalysis"/></div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="ctfQuestionList.length" :label="`CTF题（${ ctfQuestionList.length }）`" name="CTF">
        <div class="_question_list">
          <div
            v-for="(q, index) in ctfQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.questionType != 10 ? q.content : q.questionName"/></div>
              <div v-if="q.questionUserDetailsScore == 0" class="_question_item_type" style="color: #ff4b40;"> {{ q.questionUserDetailsScore }}分</div>
              <div v-else class="_question_item_type" style="color: #4cbf4b;"> {{ q.questionUserDetailsScore }}分</div>
            </div>

            <div class="_question_score">
              <div>该题：<span>{{ q.questionScore }}</span> 分</div>
              <div>学员答案：<span>{{ q.questionUserAnswer || '未作答' }}</span></div>
              <div>参考答案：<span>{{ q.questionAnswer }}</span></div>
              <div>题目解析：<span style="display: inline-block" v-html="q.questionAnalysis"/></div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="awdQuestionList.length" :label="`AWD题（${ awdQuestionList.length }）`" name="AWD">
        <div class="_question_list">
          <div
            v-for="(q, index) in awdQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.questionType != 10 ? q.content : q.questionName"/></div>
              <div v-if="q.questionUserDetailsScore == 0" class="_question_item_type" style="color: #ff4b40;"> {{ q.questionUserDetailsScore }}分</div>
              <div v-else class="_question_item_type" style="color: #4cbf4b;"> {{ q.questionUserDetailsScore }}分</div>
            </div>

            <div class="_question_score">
              <div>该题：<span>{{ q.questionScore }}</span> 分</div>
              <div>学员答案：<span>{{ q.questionUserAnswer || '未作答' }}</span></div>
              <div>参考答案：<span>{{ q.questionAnswer }}</span></div>
              <div>题目解析：<span style="display: inline-block" v-html="q.questionAnalysis"/></div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="loopholeQuestionList.length" :label="`漏洞题（${ loopholeQuestionList.length }）`" name="loophole">
        <div class="_question_list">
          <div
            v-for="(q, index) in loopholeQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.questionType != 10 ? q.content : q.questionName"/></div>
              <div v-if="q.questionUserDetailsScore == 0" class="_question_item_type" style="color: #ff4b40;"> {{ q.questionUserDetailsScore }}分</div>
              <div v-else class="_question_item_type" style="color: #4cbf4b;"> {{ q.questionUserDetailsScore }}分</div>
            </div>

            <div class="_question_score">
              <div>该题：<span>{{ q.questionScore }}</span> 分</div>
              <div>学员答案：<span>{{ q.questionUserAnswer || '未作答' }}</span></div>
              <div>参考答案：<span>{{ q.questionAnswer }}</span></div>
              <div>题目解析：<span style="display: inline-block" v-html="q.questionAnalysis"/></div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="fillBlankQuestionList.length" :label="`填空题（${ fillBlankQuestionList.length }）`" name="fillBlank">
        <div class="_question_list">
          <div
            v-for="(q, index) in fillBlankQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.questionType != 10 ? q.content : q.questionName"/></div>
              <div v-if="q.questionUserDetailsScore == 0" class="_question_item_type" style="color: #ff4b40;"> {{ q.questionUserDetailsScore }}分</div>
              <div v-else class="_question_item_type" style="color: #4cbf4b;"> {{ q.questionUserDetailsScore }}分</div>
            </div>

            <div class="_question_score">
              <div>该题：<span>{{ q.questionScore }}</span> 分</div>
              <div>学员答案：<span>{{ q.questionUserAnswer || '未作答' }}</span></div>
              <div>参考答案：<span>{{ q.questionAnswer }}</span></div>
              <div>题目解析：<span style="display: inline-block" v-html="q.questionAnalysis"/></div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="shortAnswerQuestionList.length" :label="`简答题（${ shortAnswerQuestionList.length }）`" name="shortAnswer">
        <div class="_question_list">
          <div
            v-for="(q, index) in shortAnswerQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.questionType != 10 ? q.content : q.questionName"/></div>
              <div v-if="q.questionUserDetailsScore !== null && q.markingPapersStatus == 1" :style="{ color: q.questionUserDetailsScore !== 0 ? '#4cbf4b' : '#ff4b40' }" class="_question_item_type"> {{ q.questionUserDetailsScore }}分</div>
              <div v-else class="_question_item_type">-</div>
              <div v-if="q.questionUserDetailsScore !== null && q.markingPapersStatus == 1" :style="{ color: q.questionUserDetailsScore !== 0 ? '#4cbf4b' : '#ff4b40' }" class="_question_item_type"> {{ q.questionUserDetailsScore }}分</div>
              <div v-else class="_question_item_type">-</div>
            </div>

            <div class="_question_score">
              <div>该题：<span>{{ q.questionScore }}</span> 分</div>
              <div>学员答案：<span>{{ q.questionUserAnswer || '未作答' }}</span></div>
              <div>参考答案：<span>{{ q.questionAnswer }}</span></div>
              <div>题目解析：<span style="display: inline-block" v-html="q.questionAnalysis"/></div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="synthesisQuestionList.length" :label="`组合题（${ synthesisQuestionList.length }）`" name="synthesis">
        <div v-for="(item, index) in synthesisQuestionList" :key="item.id + index" class="_question_list question_info">
          <div class="_question_item_content"><span style="font-weight: 600;" v-html="item.questionType != 10 ? item.content : item.questionName"/></div>
          <div v-for="(q, idx) in item.combinationQuestionBOS" :key="idx" class="border_comp">
            <div class="flex-space-between mb-10">
              <div class="comp-question">
                <div>综合题{{ idx + 1 }}.&nbsp;</div>
                <div style="max-width: 95%;"><span v-html="q.questionName"/>&nbsp;</div>
                <div style="position: absolute;right: 98px;">
                  <span v-if="q.questionScore !== null" :style="{ color: q.questionScore !== 0 ? '#4cbf4b' : '#ff4b40' }">({{ q.questionScore }}分)</span>
                  <span v-else style="margin-right: 15px;">-</span>
                </div>
              </div>
            </div>
            <div v-for="(sub, subIndex) in JSON.parse(q.content)" :key="subIndex" class="comp-content-wrap _question_item_content">
              <div>题目{{ subIndex + 1 }}.&nbsp;<span style="display: block; width: 95%;" v-html="sub"/>
                <span v-if="q.scoreArr !== null && item.markingPapersStatus == 1" :style="{ color: q.scoreArr[subIndex] !== 0 ? '#4cbf4b' : '#ff4b40',width: '50px' }" class="_question_item_type">{{ `(${q.scoreArr[subIndex]}分)` }}</span>
                <span v-else class="_question_item_type">-</span>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="otherQuestionList.length" :label="`其他题（${ otherQuestionList.length }）`" name="other">
        <div class="_question_list">
          <div
            v-for="(q, index) in otherQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.questionType != 10 ? q.content : q.questionName"/></div>
              <div v-if="q.questionUserDetailsScore == 0" class="_question_item_type" style="color: #ff4b40;"> {{ q.questionUserDetailsScore }}分</div>
              <div v-else class="_question_item_type" style="color: #4cbf4b;"> {{ q.questionUserDetailsScore }}分</div>
            </div>

            <div class="_question_score">
              <div>该题：<span>{{ q.questionScore }}</span> 分</div>
              <div>学员答案：<span>{{ q.questionUserAnswer || '未作答' }}</span></div>
              <div>参考答案：<span>{{ q.questionAnswer }}</span></div>
              <div>题目解析：<span style="display: inline-block" v-html="q.questionAnalysis"/></div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-empty
        v-if="!questionList.length"
        :image="img"
        :image-size="110"
        style="margin: 100px auto"
        description="暂无答题记录"
      />
    </el-tabs>
    <div class="footer">
      <div class="footer-rigt">
        <el-button type="primary" @click="prev">上一人</el-button>
        <el-button type="primary" @click="next">下一人</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { queryAnswer, studentList } from '@/api/exam/index.js'
import utils from './utils.js'

export default {
  props: {
  },
  data() {
    return {
      questionConf: utils,
      optionLabel: Array.from({ length: 26 }, (_, index) => String.fromCharCode('A'.charCodeAt(0) + index)),
      activeName: '',
      evaluationCode: this.$route.params.evaluationCode || '',
      paperTotalScore: this.$route.params.score || '',
      switchStudent: 0,
      questionList: [],
      studentListData: [],
      loading: false,
      img: require('@/assets/empty_state.png')
    }
  },
  computed: {
    getQuestionNum() {
      let emulationNum = 0
      this.synthesisQuestionList.forEach(item => {
        if (item.questionType == 10 && item.combinationQuestionBOS) {
          emulationNum += item.combinationQuestionBOS.length
        } else {
          emulationNum += 1
        }
      })
      return this.singleQuestionList.length +
      this.multiQuestionList.length +
      this.judgeQuestionList.length +
      this.ctfQuestionList.length +
      this.awdQuestionList.length +
      this.otherQuestionList.length +
      this.fillBlankQuestionList.length +
      this.shortAnswerQuestionList.length +
      this.loopholeQuestionList.length +
      emulationNum
    },
    getScore() {
      let score = 0
      this.singleQuestionList.forEach(item => {
        if (item.questionUserDetailsScore) {
          score = score + item.questionUserDetailsScore
        }
      })
      this.multiQuestionList.forEach(item => {
        if (item.questionUserDetailsScore) {
          score = score + item.questionUserDetailsScore
        }
      })
      this.judgeQuestionList.forEach(item => {
        if (item.questionUserDetailsScore) {
          score = score + item.questionUserDetailsScore
        }
      })
      this.ctfQuestionList.forEach(item => {
        if (item.questionUserDetailsScore) {
          score = score + item.questionUserDetailsScore
        }
      })
      this.awdQuestionList.forEach(item => {
        if (item.questionUserDetailsScore) {
          score = score + item.questionUserDetailsScore
        }
      })
      this.otherQuestionList.forEach(item => {
        if (item.questionUserDetailsScore) {
          score = score + item.questionUserDetailsScore
        }
      })
      this.fillBlankQuestionList.forEach(item => {
        if (item.questionUserDetailsScore) {
          score = score + item.questionUserDetailsScore
        }
      })
      this.shortAnswerQuestionList.forEach(item => {
        if (item.questionUserDetailsScore) {
          score = score + item.questionUserDetailsScore
        }
      })
      this.loopholeQuestionList.forEach(item => {
        if (item.questionUserDetailsScore) {
          score = score + item.questionUserDetailsScore
        }
      })
      this.synthesisQuestionList.forEach(item => {
        if (item.questionType == 10 && item.combinationQuestionBOS) {
          item.combinationQuestionBOS.forEach(comp => {
            if (comp.questionScore) {
              score += comp.questionScore
            }
          })
        } else {
          if (item.questionScore) {
            score = score + item.questionScore
          }
        }
      })
      return score
    },
    singleQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '1' })
    },
    multiQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '2' })
    },
    judgeQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '3' })
    },
    ctfQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '4' })
    },
    awdQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '5' })
    },
    otherQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '6' })
    },
    fillBlankQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '7' })
    },
    shortAnswerQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '8' })
    },
    loopholeQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '9' })
    },
    synthesisQuestionList() {
      const synthesisArr = this.questionList.filter(item => item.questionType == '10')
      synthesisArr.map((item, index) => {
        const scoreData = JSON.parse(item.combinationUserPoints)
        item.combinationQuestionBOS.map((questionItem, questionIndex) => {
          if (scoreData && scoreData.length > 0) {
            questionItem.questionScore = scoreData[questionIndex].map(each => Number(each)).reduce((p, q) => p + q)
            questionItem.scoreArr = scoreData[questionIndex] // 子题干的分数
          }
        })
        item.questionScore = JSON.parse(item.combinationPoints)[0].map(each => Number(each)).reduce((p, q) => p + q) // 主题干的分数
        // 给每个题目标题加序号
        item.questionName = `组合题${index + 1}.` + item.questionName
      })
      return synthesisArr
    },
    questionTypes() {
      return [
        this.judgeQuestionList.length > 0 ? 'emulation' : null,
        this.singleQuestionList.length > 0 ? 'theory' : null,
        this.multiQuestionList.length > 0 ? 'target' : null,
        this.ctfQuestionList.length > 0 ? 'CTF' : null,
        this.awdQuestionList.length > 0 ? 'AWD' : null,
        this.loopholeQuestionList.length > 0 ? 'loophole' : null,
        this.fillBlankQuestionList.length > 0 ? 'fillBlank' : null,
        this.shortAnswerQuestionList.length > 0 ? 'shortAnswer' : null,
        this.synthesisQuestionList.length > 0 ? 'synthesis' : null,
        this.otherQuestionList.length > 0 ? 'other' : null
      ]
    },
    numberedQuestionTypes() {
      return this.questionTypes.filter(type => type !== null).map((type, index) => ({ index, type }))
    }
  },
  watch: {
    numberedQuestionTypes: {
      handler(newVal) {
        if (newVal) {
          this.activeName = newVal[0].type
        }
      },
      deep: true
    }
  },
  mounted() {
    this.getStudentList()
  },
  methods: {
    prev() {
      if (this.switchStudent == 0) {
        this.$message.error('该考生为第一位考生')
        return
      }
      this.evaluationCode = this.studentListData[this.switchStudent - 1].evaluationCode
      this.switchStudent = this.switchStudent - 1

      const studentItem = this.studentListData.find(item => item.evaluationCode == this.evaluationCode)
      this.$router.push({
        name: 'answerRecordTraining',
        params: {
          id: studentItem.id,
          realname: studentItem.realname,
          userId: studentItem.userId,
          examId: this.$route.params.examId,
          examName: this.$route.params.examName,
          examCode: studentItem.examCode,
          evaluationCode: studentItem.evaluationCode,
          examStatus: studentItem.examStatus,
          schedulingCode: this.$route.params.schedulingCode,
          view: 'questionInfo' }
      })
      this.getQuestion()
    },
    next() {
      if (this.switchStudent == this.studentListData.length - 1) {
        this.$message.error('该考生已是最后一位考生')
        return
      }
      this.evaluationCode = this.studentListData[this.switchStudent + 1].evaluationCode
      this.switchStudent = this.switchStudent + 1

      const studentItem = this.studentListData.find(item => item.evaluationCode == this.evaluationCode)
      this.$router.push({
        name: 'answerRecordTraining',
        params: {
          id: studentItem.id,
          realname: studentItem.realname,
          userId: studentItem.userId,
          examId: this.$route.params.examId,
          examName: this.$route.params.examName,
          examCode: studentItem.examCode,
          evaluationCode: studentItem.evaluationCode,
          examStatus: studentItem.examStatus,
          schedulingCode: this.$route.params.schedulingCode,
          view: 'questionInfo' }
      })
      this.getQuestion()
    },
    // 获取参数学生列表
    getStudentList: function() {
      const params = {
        page: 1,
        limit: 10000,
        // examStatus: 1,
        examId: this.$route.params.examId,
        schedulingCode: this.$route.params.schedulingCode
      }
      studentList(params).then((res) => {
        if (res.code === 0) {
          this.studentListData = res.data.records
          this.studentListParam = this.studentListData.find(item => item.evaluationCode == this.evaluationCode)
          this.evaluationCode = this.studentListParam && this.studentListParam.evaluationCode

          this.switchStudent = this.studentListData.map(item => item.id).indexOf(this.$route.params.id)
          this.getQuestion()
        }
      })
    },
    getQuestion() {
      this.loading = true
      const { userId, examId, examCode } = this.$route.params
      const params = {
        userId,
        examCode,
        evaluationCode: this.evaluationCode,
        examinationId: examId,
        submitType: ''
      }
      queryAnswer(params).then((res) => {
        if (res.code === 0) {
          this.questionList = res.data
          this.loading = false
        }
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-question-bank {
  position: relative;
  ._paper_search {
    position: absolute;
    right: 30px;
    display: flex;
    ._paper_search_1 {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }
  /deep/ .el-tabs {
    display: flex;
    flex-direction: column;
    height: 95%;
    .el-tabs__header {
      .el-tabs__item {
        margin-right: 0;
        border: none !important;
      }
    }
    .el-tabs__content {
      height: 99%;
      overflow-y: auto;
    }
    ._question_list {
      ._question_item {
        width: 99%;
        padding: 15px 20px;
        min-height: 90px;
        background: #ffffff;
        display: flex;
        flex-direction: column;
        font-size: 14px;
        color: #4e5969;
        position: relative;
        border: 1px solid #e5e6eb;
        border-radius: 4px;
        margin-bottom: 10px;
        ._question_option {
          margin-top: 10px;
          margin-left: 15px;
          font-size: 14px;
          color: #4e5969;
          display: flex;
          flex-direction: column;
          line-height: 22px;
          .el-radio {
            margin-bottom: 8px;
            display: flex;
            align-items: flex-start;
            .el-radio__label {
              font-size: 14px;
              color: #4e5969;
              white-space: normal;
              word-break: break-all;
            }
          }
          .el-checkbox {
            margin-bottom: 8px;
            display: flex;
            align-items: flex-start;
            .el-checkbox__label {
              font-size: 14px;
              color: #4e5969;
              white-space: normal;
              word-break: break-all;
            }
          }
        }
        ._question_score {
          border-top: 1px solid #e5e6eb;
          padding: 10px 0 0 0 ;

          ._question_delete {
            color: #F56C6C;
            cursor: pointer;
          }
        }
        ._question_item_content {
          display: flex;
          max-height: 200px;
          overflow-y: auto;
          margin-right: 55px;
        }
        ._question_item_type {
          position: absolute;
          right: 20px;
          top: 5px;
        }
      }
      ._question_item_check {
        border: 1px solid var(--color-600);
      }
      .border_comp {
        border: 1px solid #d1d1d1;
        border-radius: 10px;
        box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
        margin: 10px;
        padding: 10px;
      }
      .comp-question {
        display: flex;
        max-height: 200px;
        width: 98%;
        overflow-y: auto;
        font-weight: 600;
        font-size: 14px;
        color: rgb(36, 41, 47);
        margin-bottom: 10px;
        margin: 10px 0px;
        >span {
          word-break: break-all;
        }
      }
      .comp-content-wrap {
        border: 1px solid #e5e6eb;
        margin: 5px 11px 10px 9px;
        padding: 10px 20px;
        >div:first-child {
          display: flex;
          max-height: 200px;
          overflow-y: auto;
          margin-bottom: 10px;
          >span {
            word-break: break-all;
          }
        }
      }
    }
  }
  .footer {
    margin: 10px 16px;
    position: relative;
    .footer-rigt {
      position: absolute;
      right: 8px;
    }
}
}
.question_info {
  margin-left: 6px;
  margin-right: 17px;
  padding: 10px;
  font-size: 14px;
  border: 1px solid #d1d1d1;
  border-radius: 10px;
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
}
</style>
