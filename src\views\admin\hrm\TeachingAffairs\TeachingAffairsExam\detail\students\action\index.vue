<template>
  <div class="buttons-wrap">
    <el-button :disabled="singleDisabled" type="primary" @click="currentClickDrop('examReviewTraining')">阅卷</el-button>
    <el-button type="primary" @click="currentClickDrop('exportAll')">导出</el-button>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :search-name="searchName"
          :student-detail-id="studentDetailId"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import examReview from './modal-review'
import exportAll from './modal-export'
import detail from './modal-detail'
import { queryById } from '@/api/exam/index.js'

export default {
  components: {
    examReview,
    exportAll,
    detail
  },
  mixins: [mixinsActionMenu],
  props: {
    searchName: {
      type: Object
    }
  },
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'examReview': '阅卷',
        'exportAll': '导出',
        'detail': '考生详情'
      },
      userId: '',
      dialogLoading: false,
      confirmDisabled: false,
      studentDetailId: '',
      markingPapersType: true
    }
  },
  computed: {
    isAdmin() {
      return this.$store.state.user.userInfo.roleId == '180162'
    }
  },
  mounted() {
    this.userId = this.$store.state.user.userInfo.userId
    this.getEaxmById()
    this.$bus.on('studentDetail', (id) => {
      this.studentDetailId = id
      this.clickDrop('detail')
    })
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      } else if (type === 'confirmDisabled') {
        this.confirmDisabled = data
      }
    },
    handleExam(name) {
      // 进入阅卷页面，把老师打的分数清除
      for (let i = 0; i < localStorage.length; i++) {
        if (localStorage.key(i).includes('soreList')) {
          localStorage.removeItem(localStorage.key(i))
        }
      }
      this.$router.push({
        name: name,
        query: {
          ...this.$route.query,
          userId: this.selectItem[0].userId,
          examCode: this.selectItem[0].examCode,
          evaluationCode: this.selectItem[0].evaluationCode,
          examinationId: this.$route.params.id,
          submitType: '1',

          oneLevelTitle: '考试管理',
          oneLevelName: 'ca-exam',
          twoLevelTitle: this.$route.params.name,
          twoLevelName: 'studentsList',
          examName: this.$route.params.examName,
          schedulingCode: this.$route.params.schedulingCode
        }
      })
    },
    getEaxmById() {
      const id = Number(this.$route.params.id)
      queryById({ id: id }).then((res) => {
        if (res.code === 0) {
          this.markingPapersType = res.data.markingPapersType
        }
      })
    },
    currentClickDrop(name) {
      if (name === 'examReviewTraining') {
        localStorage.removeItem('shortMarking')
        localStorage.removeItem('shortScore')
        this.handleExam(name)
        return
      }
      this.modalName = name
    }
  }
}
</script>
