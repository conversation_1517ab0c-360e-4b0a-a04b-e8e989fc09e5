<template>
  <create-view :loading="loading" title="编辑检测申请">
    <div slot="content">
      <el-form
        ref="ruleForm"
        :model="formData"
        :rules="rules"
        label-position="left"
        label-width="130px"
      >
        <!-- 文件上传用隐藏input -->
        <input
          ref="fileRef"
          type="file"
          style="display: none"
          @change="handleChange"
        >
        <!-- 厂商信息 -->
        <el-card>
          <el-divider content-position="left">厂商信息</el-divider>
          <el-row
            v-for="(field, index) in manufacturerFields"
            :key="index"
            :gutter="24"
          >
            <!-- 全宽字段 -->
            <template v-if="field.fullWidth">
              <el-col :span="24">
                <el-form-item
                  :label="field.fullWidth.label"
                  :prop="field.fullWidth.key"
                >
                  <!-- 多行文本 -->
                  <el-input
                    v-if="field.fullWidth.type === 'textarea'"
                    v-model.trim="formData[field.fullWidth.key]"
                    :rows="4"
                    type="textarea"
                    placeholder="请输入"
                    maxlength="255"
                  />
                  <!-- 富文本编辑器 -->
                  <myEditor
                    v-else-if="field.fullWidth.type === 'richtext'"
                    :key="timer + field.fullWidth.key"
                    :editor-config="blurEditorFocusConfig"
                    :content="formData[field.fullWidth.key] || ''"
                    :only-editor="endType"
                    :is-read-only="endType"
                    :id-prefix="field.fullWidth.key"
                    :upload-img-api="richTextTestingUploadImgApi"
                    width="100%"
                    height="200px"
                    @contentChange="contentChange(field.fullWidth.key, $event)"
                  />
                </el-form-item>
              </el-col>
            </template>

            <!-- 两列布局字段 -->
            <template v-else>
              <el-col :span="12">
                <el-form-item :label="field.left.label" :prop="field.left.key">
                  <!-- 文本输入框 -->
                  <el-input
                    v-if="field.left.type === 'text'"
                    v-model.trim="formData[field.left.key]"
                    placeholder="请输入"
                  />
                  <!-- 纯文本展示 -->
                  <span v-else-if="field.left.type === 'textonly'">{{
                    formData[field.left.key] || "-"
                  }}</span>
                  <!-- 下拉选择框 -->
                  <el-select
                    v-else-if="field.left.type === 'select'"
                    :disabled="field.left.disabled"
                    v-model="formData[field.left.key]"
                    filterable
                    clearable
                    placeholder="请选择"
                    style="width: 100%;"
                  >
                    <el-option
                      v-for="item in field.left.options || []"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <!-- 单选框组 -->
                  <el-radio-group
                    v-else-if="field.left.type === 'radio'"
                    v-model="formData[field.left.key]"
                  >
                    <el-radio
                      v-for="item in field.left.options || []"
                      :key="item.value"
                      :label="item.value"
                    >{{ item.label }}</el-radio
                    >
                  </el-radio-group>
                  <!-- 多选框组 -->
                  <el-checkbox-group
                    v-else-if="field.left.type === 'checkbox'"
                    v-model="formData[field.left.key]"
                  >
                    <el-checkbox
                      v-for="item in field.left.options || []"
                      :key="item.value"
                      :label="item.value"
                    >{{ item.label }}</el-checkbox
                    >
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
              <el-col v-if="field.right" :span="12">
                <el-form-item
                  :label="field.right.label"
                  :prop="field.right.key"
                >
                  <!-- 文本输入框 -->
                  <el-input
                    v-if="field.right.type === 'text'"
                    v-model.trim="formData[field.right.key]"
                    placeholder="请输入"
                  />
                  <!-- 纯文本展示 -->
                  <span v-else-if="field.right.type === 'textonly'">{{
                    formData[field.right.key] || "-"
                  }}</span>
                  <!-- 下拉选择框 -->
                  <el-select
                    v-else-if="field.right.type === 'select'"
                    :disabled="field.right.disabled"
                    v-model="formData[field.right.key]"
                    filterable
                    clearable
                    placeholder="请选择"
                    style="width: 100%;"
                  >
                    <el-option
                      v-for="item in field.right.options || []"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <!-- 单选框组 -->
                  <el-radio-group
                    v-else-if="field.right.type === 'radio'"
                    v-model="formData[field.right.key]"
                  >
                    <el-radio
                      v-for="item in field.right.options || []"
                      :key="item.value"
                      :label="item.value"
                    >{{ item.label }}</el-radio
                    >
                  </el-radio-group>
                  <!-- 多选框组 -->
                  <el-checkbox-group
                    v-else-if="field.right.type === 'checkbox'"
                    v-model="formData[field.right.key]"
                  >
                    <el-checkbox
                      v-for="item in field.right.options || []"
                      :key="item.value"
                      :label="item.value"
                    >{{ item.label }}</el-checkbox
                    >
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </template>
          </el-row>

        </el-card>
        <el-card class="mt-10">
          <el-divider content-position="left">产品信息</el-divider>
          <el-row
            v-for="(field, index) in productFields"
            :key="`product-${index}`"
            :gutter="24"
          >
            <!-- 全宽字段 -->
            <template v-if="field.fullWidth">
              <el-col :span="24">
                <el-form-item
                  :label="field.fullWidth.label"
                  :prop="field.fullWidth.key"
                >
                  <!-- 多行文本 -->
                  <el-input
                    v-if="field.fullWidth.type === 'textarea'"
                    v-model.trim="formData[field.fullWidth.key]"
                    :rows="4"
                    type="textarea"
                    placeholder="请输入"
                    maxlength="255"
                  />
                  <!-- 富文本编辑器 -->
                  <myEditor
                    v-else-if="field.fullWidth.type === 'richtext'"
                    :key="timer + field.fullWidth.key"
                    :editor-config="blurEditorFocusConfig"
                    :content="formData[field.fullWidth.key] || ''"
                    :only-editor="endType"
                    :is-read-only="endType"
                    :id-prefix="field.fullWidth.key"
                    :upload-img-api="richTextTestingUploadImgApi"
                    width="100%"
                    height="200px"
                    @contentChange="contentChange(field.fullWidth.key, $event)"
                  />
                </el-form-item>
              </el-col>
            </template>

            <!-- 两列布局字段 -->
            <template v-else>
              <el-col :span="12">
                <el-form-item :label="field.left.label" :prop="field.left.key">
                  <!-- 文本输入框 -->
                  <el-input
                    v-if="field.left.type === 'text'"
                    v-model.trim="formData[field.left.key]"
                    placeholder="请输入"
                  />
                  <!-- 纯文本展示 -->
                  <span v-else-if="field.left.type === 'textonly'">{{
                    formData[field.left.key] || "-"
                  }}</span>
                  <!-- 下拉选择框 -->
                  <el-select
                    v-else-if="field.left.type === 'select'"
                    v-model="formData[field.left.key]"
                    filterable
                    clearable
                    placeholder="请选择"
                    style="width: 100%;"
                  >
                    <el-option
                      v-for="item in field.left.options || []"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <!-- 单选框组 -->
                  <el-radio-group
                    v-else-if="field.left.type === 'radio'"
                    v-model="formData[field.left.key]"
                  >
                    <el-radio
                      v-for="item in field.left.options || []"
                      :key="item.value"
                      :label="item.value"
                    >{{ item.label }}</el-radio
                    >
                  </el-radio-group>
                  <!-- 多选框组 -->
                  <el-checkbox-group
                    v-else-if="field.left.type === 'checkbox'"
                    v-model="formData[field.left.key]"
                  >
                    <el-checkbox
                      v-for="item in field.left.options || []"
                      :key="item.value"
                      :label="item.value"
                    >{{ item.label }}</el-checkbox
                    >
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
              <el-col v-if="field.right" :span="12">
                <el-form-item
                  :label="field.right.label"
                  :prop="field.right.key"
                >
                  <!-- 文本输入框 -->
                  <el-input
                    v-if="field.right.type === 'text'"
                    v-model.trim="formData[field.right.key]"
                    placeholder="请输入"
                  />
                  <!-- 纯文本展示 -->
                  <span v-else-if="field.right.type === 'textonly'">{{
                    formData[field.right.key] || "-"
                  }}</span>
                  <!-- 下拉选择框 -->
                  <el-select
                    v-else-if="field.right.type === 'select'"
                    v-model="formData[field.right.key]"
                    filterable
                    clearable
                    placeholder="请选择"
                    style="width: 100%;"
                  >
                    <el-option
                      v-for="item in field.right.options || []"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <!-- 单选框组 -->
                  <el-radio-group
                    v-else-if="field.right.type === 'radio'"
                    v-model="formData[field.right.key]"
                  >
                    <el-radio
                      v-for="item in field.right.options || []"
                      :key="item.value"
                      :label="item.value"
                    >{{ item.label }}</el-radio
                    >
                  </el-radio-group>
                  <!-- 多选框组 -->
                  <el-checkbox-group
                    v-else-if="field.right.type === 'checkbox'"
                    v-model="formData[field.right.key]"
                  >
                    <el-checkbox
                      v-for="item in field.right.options || []"
                      :key="item.value"
                      :label="item.value"
                    >{{ item.label }}</el-checkbox
                    >
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </template>
          </el-row>
        </el-card>
        <!-- 其他信息 -->
        <el-card class="mt-10">
          <el-divider content-position="left">其他信息</el-divider>
          <el-form-item
            v-for="(field, index) in attachmentFields"
            :key="`attachment-${index}`"
            :label="field.label"
            :prop="field.key"
          >
            <!-- 多行文本框 -->
            <el-input
              v-if="field.type === 'textarea'"
              v-model.trim="formData[field.key]"
              :rows="4"
              type="textarea"
              placeholder="请输入"
              maxlength="255"
            />
            <!-- 富文本编辑器 -->
            <myEditor
              v-else-if="field.type === 'richtext'"
              :editor-config="blurEditorFocusConfig"
              :key="timer + field.key"
              :content="formData[field.key] || ''"
              :only-editor="endType"
              :is-read-only="endType"
              :id-prefix="field.key"
              :upload-img-api="richTextTestingUploadImgApi"
              width="100%"
              height="200px"
              @contentChange="contentChange(field.key, $event)"
            />
            <!-- 电子签名 -->
            <template v-else-if="field.type === 'signature'">
              <span slot="label">
                <span>{{ field.label }}</span>
                <el-tooltip transfer>
                  <i class="el-icon-warning-outline" />
                  <div slot="content">支持图片格式：jpg、jpeg、png，建议尺寸160*80像素，大小不超过10MB</div>
                </el-tooltip>
              </span>
              <div v-if="formData[field.key]" class="img-container">
                <i
                  class="el-icon-error delete-btn"
                  @click="clearFileName(field.key)"
                />
                <img
                  v-show="formData[field.key]"
                  :src="formData[field.key]"
                  alt=""
                  @click="handleClick(field.key)"
                >
              </div>
              <el-button
                v-show="!formData[field.key]"
                :disabled="endType"
                type="ghost"
                @click="handleClick(field.key)"
              >点击上传</el-button
              >
            </template>
            <!-- 附件上传 -->
            <el-upload
              v-else-if="field.type === 'file'"
              :data="{ sourceId: sourceId }"
              :headers="{ 'Admin-Token': token }"
              :http-request="httpRequest1"
              :before-upload="handleUpload1"
              :on-preview="handlePreview1"
              :on-remove="handleRemove"
              :file-list="fileList1"
              class="upload-demo1"
              action="/api/testing/testProcesses/uploadFile"
              multiple
            >
              <el-button type="ghost">上传附件</el-button>
              <div slot="tip" class="el-upload__tip">
                单个附件大小不超过{{ maxFileSize }}MB
              </div>
            </el-upload>
          </el-form-item>
        </el-card>
      </el-form>
    </div>
    <div slot="footer">
      <el-button type="text" @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确定</el-button>
    </div>
  </create-view>
</template>

<script>
import {
  deleteSignatureAttachment,
  detectionApplicationUpdate,
  electronicSignatureAttachmentUpload,
  getApplicationForm,
  getContactByVendorId,
  getSignatureAttachment,
  getVendorByUserId,
  uploadApplicationFile,
  vendorList
} from '@/api/testing/testingApplication.js'
import { richTextTestingUploadImgApi } from '@/components/testing/utils/config.js'
import createView from '@/packages/create-view/index'
import myEditor from '@/packages/editor/index.vue'
import { picturePreview } from '@/packages/utils/downloadAndPreview.js'
import validate from '@/packages/validate'
import { mapGetters } from 'vuex'
import module from '../config'

export default {
  name: 'TestingApplicationCreate',
  components: {
    createView,
    myEditor
  },
  data() {
    return {
      richTextTestingUploadImgApi,
      blurEditorFocusConfig: {
        placeholder: '请输入',
        autoFocus: false
      },
      module,
      loading: false,
      validate: validate,
      formData: {},
      formFields: [],
      rules: {},
      fileList1: [],
      currentFileIndex1: -1,
      uploadStatus: 0,
      signatureMaxFileSize: 10,
      maxFileSize: localStorage.getItem('maxFileSize'),
      maxSignatureFileSize: 10,
      token: '',
      endType: false,
      uploadName: '',
      sourceId: '',
      timer: '1000', // 用于强制刷新富文本编辑器
      processedFieldsMap: {}, // 记录已处理的字段
      // 用于动态Watch所有字段
      fieldWatchers: [],
      // 记录字段的联动关系
      fieldLinkMap: {},
      signatureObj: {},
      // 厂商名称和联系人名称字段（用于提交）
      vendorName: {
        uuid: 'vendorName',
        key: 'vendorName',
        label: '厂商名称',
        value: '', // 字段值
        type: 'text',
        defaultValue: '',
        section: 'manufacturer',
        required: false,
        show: false // false 隐藏
      },
      contactPersonName: {
        uuid: 'contactPersonName',
        key: 'contactPersonName',
        label: '厂商联系人名称',
        value: '', // 字段值
        type: 'text',
        defaultValue: '',
        section: 'manufacturer',
        required: false,
        show: false // false 隐藏
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    // 获取各区域的表单字段并按两列排列处理
    manufacturerFields() {
      return this.getPairedFields('manufacturer')
    },
    productFields() {
      return this.getPairedFields('product')
    },
    attachmentFields() {
      return this.formFields.filter(
        f => f.section === 'attachment' && f.show === true
      )
    }
  },
  watch: {
    // 动态监听会在mounted中设置
  },
  created() {
    // 初始化表单字段联动映射关系
    this.initFieldLinkMap()
  },
  mounted() {
    this.token = JSON.parse(localStorage.getItem('Admin-Token')).data
    // 获取后端返回的表单字段配置
    this.fetchFormFields()
  },
  beforeDestroy() {
    // 清理动态创建的watcher
    this.destroyWatchers()
  },
  methods: {
    // 初始化字段联动映射
    initFieldLinkMap() {
      this.fieldLinkMap = {}
    },

    // 添加动态监听器
    setupWatchers() {
      this.destroyWatchers() // 先清理已有的watcher

      // 遍历所有需要动态监听的字段
      this.formFields.forEach(field => {
        if (field.change) {
          const watcher = this.$watch(
            `formData.${field.key}`,
            (newValue, oldValue) => {
              this.handleFieldChange(field, newValue, oldValue)
            },
            { immediate: false }
          )
          this.fieldWatchers.push(watcher)
        }
      })
    },

    // 清理动态watcher
    destroyWatchers() {
      if (this.fieldWatchers && this.fieldWatchers.length) {
        this.fieldWatchers.forEach(unwatch => unwatch())
        this.fieldWatchers = []
      }
    },

    // 处理字段变化
    handleFieldChange(field, newValue, oldValue) {
      if (!newValue || newValue === oldValue) return

      // 处理厂商名称变化，获取联系人列表
      if (field.uuid === 'vendorId') {
        this.getContactsByVendorId(newValue)
      }

      // 处理联系人变化，设置联系方式
      if (field.uuid === 'contactPerson') {
        this.setContactInfo(newValue)
      }
    },

    // 根据厂商ID获取联系人列表
    async getContactsByVendorId(vendorId) {
      if (!vendorId) return

      try {
        const res = await getContactByVendorId(vendorId)

        if (res.code === 0 || res.code === 200) {
          // 获取联系人列表并格式化
          const contactList = res.data || []
          const options = contactList.map(item => ({
            value: String(item.contactId),
            label: item.contactName,
            // 存储额外信息用于后续联动
            contactPhone: item.contactPhone
          }))

          // 更新联系人下拉选项
          this.updateFieldOptions('contactPerson', options)

          // 保存当前选中的联系人ID，如果用户手动更改了厂商，需要清空联系人
          if (!options.some(opt => opt.value === this.formData.contactPerson)) {
            this.formData.contactPerson = ''
          }
        }
      } catch (error) {
        console.error('获取联系人列表失败:', error)
      }
    },

    // 设置联系方式
    setContactInfo(contactPersonId) {
      if (!contactPersonId) return

      // 查找选中的联系人选项
      const contactPersonField = this.formFields.find(
        f => f.uuid === 'contactPerson'
      )
      if (!contactPersonField || !contactPersonField.options) return

      const selectedContact = contactPersonField.options.find(
        opt => opt.value === contactPersonId
      )
      if (selectedContact && selectedContact.contactPhone) {
        this.formData.contactInfo = selectedContact.contactPhone
      }
    },

    // 更新字段选项
    updateFieldOptions(uuid, options) {
      const fieldIndex = this.formFields.findIndex(f => f.uuid === uuid)
      if (fieldIndex !== -1) {
        // 使用Vue.set确保响应式更新
        this.$set(this.formFields[fieldIndex], 'options', options)
      }
    },

    // 获取厂商列表
    async getVendorList() {
      try {
        this.loading = true
        const res = await vendorList()
        this.loading = false

        if (res.code === 0 || res.code === 200) {
          const vendorOptions = (res.data || []).map(item => ({
            value: String(item.id),
            label: item.name
          }))

          // 更新厂商下拉选项
          this.updateFieldOptions('vendorId', vendorOptions)
        }
      } catch (error) {
        console.error('获取厂商列表失败:', error)
        this.loading = false
      }
    },

    // 获取检测厂商的厂商
    async getAccountVendor() {
      const res = await getVendorByUserId()
      if (res.code === 0 || res.code === 200) {
        // 如果获取到厂商信息，设置厂商ID并禁用选择
        if (res.data && res.data.length) {
          // 找到厂商ID字段
          const vendorField = this.formFields.find(f => f.uuid === 'vendorId')
          if (vendorField) {
            // 设置字段值
            this.formData[vendorField.key] = String(res.data[0].id)
            // 禁用字段
            // this.$set(vendorField, 'disabled', true)
            // 更新选项
            this.$set(vendorField, 'options', res.data.map(item => {
              return {
                value: String(item.id),
                label: item.name
              }
            }))

            // 根据厂商ID获取联系人列表
            this.getContactsByVendorId(String(res.data[0].id))
          }
        }
      }
    },

    // 初始化表单选项
    async initFieldOptions() {
      const roleIds = JSON.parse(this.userInfo.roleIds)
      if (roleIds.length === 1 && roleIds[0] == 181254) {
        // 如果用户只有厂商角色，先获取厂商信息
        await this.getAccountVendor()
      } else {
        // 获取所有需要初始化选项的字段
        const fieldsWithInitOptions = this.formFields.filter(
          field => field.initOptions
        )

        for (const field of fieldsWithInitOptions) {
          if (field.uuid === 'vendorId') {
            await this.getVendorList()

            // 如果已经选择了厂商，获取该厂商的联系人列表
            const vendorIdField = this.formFields.find(f => f.uuid === 'vendorId')
            if (vendorIdField && this.formData[vendorIdField.key]) {
              // 获取该厂商的联系人列表
              await this.getContactsByVendorId(this.formData[vendorIdField.key])
            }
          }
          // 这里可以添加其他需要初始化的字段
        }
      }
    },

    // 获取两列排列的字段
    getPairedFields(section) {
      // 筛选出show为true的字段
      const sectionFields = this.formFields.filter(
        f => f.section === section && f.show === true
      )
      const result = []

      // 文本框和富文本都占一行
      const fullWidthFields = sectionFields.filter(
        f => f.type === 'textarea' || f.type === 'richtext'
      )
      // 其他字段两列排列
      const normalFields = sectionFields.filter(
        f => f.type !== 'textarea' && f.type !== 'richtext'
      )

      // 处理常规字段，两个一组
      for (let i = 0; i < normalFields.length; i += 2) {
        const row = {
          left: normalFields[i],
          right: normalFields[i + 1] || null // 可能没有右侧字段
        }
        result.push(row)
      }

      // 处理文本域字段，每个独占一行
      fullWidthFields.forEach(field => {
        result.push({
          fullWidth: field
        })
      })

      return result
    },

    // 获取后端字段配置
    async fetchFormFields() {
      try {
        const formRes = await getApplicationForm(this.$route.params.id)

        // 解析后端返回的表单配置
        let formFields = []
        if (formRes && formRes.data && formRes.data.template) {
          try {
            formFields = JSON.parse(formRes.data.template)
            // 确保所有字段都有show属性，默认为true
            formFields = formFields.map(field => ({
              ...field,
              show: field.show !== undefined ? field.show : true
            }))
          } catch (e) {
            console.error('解析表单配置失败:', e)
            formFields = []
          }
        }

        // 如果后端没有返回配置或解析失败，使用默认配置
        if (!formFields || !formFields.length) {
          // 这里模拟后端返回的表单字段配置
          formFields = [
            {
              uuid: 'vendorId', // 唯一标识
              key: 'vendorId', // 字段名
              label: '厂商名称', // 字段label
              value: '', // 字段值
              type: 'select', // 字段类型，除此外还有text:输入框，textonly：文字，textarea：多行输入，richtext：富文本,radio：单选，checkbox：多选，file：附件，signature：签名
              defaultValue: '', // 默认值
              section: 'manufacturer', // 属于哪部分
              required: true, // 必填
              change: true, // 需要动态watch
              initOptions: true, // 初始就要获取options
              options: [], // 选项数据
              link: ['contactPerson'], // 联动字段，需要与联动字段的uuid相同
              show: true, // false 隐藏
              validates: [] // 校验
            },
            {
              uuid: 'contractor',
              key: 'contractor',
              value: '', // 字段值
              label: '系统承建单位',
              type: 'text',
              defaultValue: '',
              section: 'manufacturer',
              required: false,
              show: true // false 隐藏
            },
            {
              uuid: 'contactPerson',
              key: 'contactPerson',
              value: '', // 字段值
              label: '厂商联系人',
              type: 'select',
              defaultValue: '',
              section: 'manufacturer',
              required: true,
              initOptions: false, // 初始就要获取options
              change: true, // 需要动态watch
              show: true, // false 隐藏
              link: ['contactInfo'],
              options: [],
              validates: []
            },
            {
              uuid: 'contactInfo',
              key: 'contactInfo',
              value: '', // 字段值
              label: '联系方式',
              type: 'textonly',
              defaultValue: '',
              section: 'manufacturer',
              required: false,
              show: true // false 隐藏
            },
            {
              uuid: 'productName',
              key: 'productName',
              value: '', // 字段值
              label: '检测产品',
              type: 'text',
              defaultValue: '',
              section: 'product',
              required: true,
              show: true, // false 隐藏
              validates: [{ error_message: '1-64个字符', regular: '^.{1,64}$' }]
            },
            {
              uuid: 'deploymentLocation',
              key: 'deploymentLocation',
              value: '', // 字段值
              label: '系统部署地点',
              type: 'text',
              defaultValue: '',
              section: 'product',
              show: true, // false 隐藏
              required: false
            },
            {
              uuid: 'version',
              key: 'version',
              value: '', // 字段值
              label: '版本号',
              type: 'text',
              defaultValue: '',
              section: 'product',
              required: true,
              show: true, // false 隐藏
              validates: [{ error_message: '1-64个字符', regular: '^.{1,64}$' }]
            },
            {
              uuid: 'investmentSource',
              key: 'investmentSource',
              value: '', // 字段值
              label: '投资来源',
              type: 'text',
              defaultValue: '',
              section: 'product',
              show: true, // false 隐藏
              required: false
            },
            {
              uuid: 'systemCategory',
              key: 'systemCategory',
              value: '', // 字段值
              label: '系统类别',
              type: 'select',
              defaultValue: '',
              section: 'product',
              show: true, // false 隐藏
              required: false,
              options: [
                { label: '统推', value: '统推' },
                { label: '自建', value: '自建' },
                { label: '其他', value: '其他' }
              ]
            },
            {
              uuid: 'constructionMethod',
              key: 'constructionMethod',
              value: '', // 字段值
              label: '建设方式',
              type: 'select',
              defaultValue: '',
              section: 'product',
              required: false,
              show: true, // false 隐藏
              options: [
                { label: '新建', value: '新建' },
                { label: '增加功能模块', value: '增加功能模块' }
              ]
            },
            {
              uuid: 'mainModules',
              key: 'mainModules',
              value: '', // 字段值
              label: '系统主要功能模块',
              type: 'richtext',
              defaultValue: '',
              section: 'product',
              required: false,
              show: true // false 隐藏
            },
            {
              uuid: 'additionalModules',
              key: 'additionalModules',
              value: '', // 字段值
              label: '系统增加功能模块',
              type: 'richtext',
              defaultValue: '',
              section: 'product',
              required: false,
              show: true // false 隐藏
            },
            {
              uuid: 'remarks',
              key: 'remarks',
              value: '', // 字段值
              label: '备注',
              type: 'richtext',
              defaultValue: '',
              section: 'attachment',
              required: false,
              show: true // false 隐藏
            },
            {
              uuid: 'signatureId',
              key: 'signatureId',
              value: '', // 字段值
              label: '电子签名',
              type: 'signature',
              defaultValue: '',
              section: 'attachment',
              required: true,
              show: true // false 隐藏
            },
            {
              uuid: 'attachmentIds',
              key: 'attachmentIds',
              value: [], // 字段值，初始化为空数组
              label: '附件',
              type: 'file',
              defaultValue: '',
              section: 'attachment',
              required: false,
              show: true // false 隐藏
            },
            {
              uuid: 'attachmentFiles',
              key: 'attachmentFiles',
              value: [], // 初始化为空数组
              label: '附件文件列表',
              type: 'hidden',
              defaultValue: '',
              section: 'hidden',
              required: false,
              show: false // 隐藏字段
            }
          ]
        }

        // 更新字段的value值，从接口返回的values中获取
        if (formRes.data && formRes.data.values) {
          // 先处理attachmentIds和attachmentFiles
          let attachmentIds = []
          let attachmentFilesData = []

          // 处理attachmentIds
          if (formRes.data.values.attachmentIds) {
            if (typeof formRes.data.values.attachmentIds === 'string') {
              attachmentIds = formRes.data.values.attachmentIds.split(',').filter(id => id.trim() !== '')
            } else if (Array.isArray(formRes.data.values.attachmentIds)) {
              attachmentIds = formRes.data.values.attachmentIds
            }
          }

          // 处理attachmentFiles
          if (formRes.data.values.attachmentFiles) {
            if (typeof formRes.data.values.attachmentFiles === 'string') {
              try {
                attachmentFilesData = JSON.parse(formRes.data.values.attachmentFiles)
                if (!Array.isArray(attachmentFilesData)) {
                  attachmentFilesData = []
                }
              } catch (e) {
                console.error('解析附件文件列表失败:', e)
                attachmentFilesData = []
              }
            } else if (Array.isArray(formRes.data.values.attachmentFiles)) {
              attachmentFilesData = formRes.data.values.attachmentFiles
            }
          }

          // 更新所有字段的值
          formFields.forEach(field => {
            if (field.uuid === 'attachmentIds') {
              field.value = attachmentIds
            } else if (field.uuid === 'attachmentFiles') {
              field.value = attachmentFilesData
            } else if (formRes.data.values[field.key] !== undefined) {
              field.value = formRes.data.values[field.key]
            }
          })

          // 初始化vendorName和contactPersonName的值
          if (formRes.data.values.vendorName !== undefined) {
            this.vendorName.value = formRes.data.values.vendorName
          }
          if (formRes.data.values.contactPersonName !== undefined) {
            this.contactPersonName.value = formRes.data.values.contactPersonName
          }
        }

        // 确保attachmentFiles字段存在
        if (!formFields.some(field => field.uuid === 'attachmentFiles')) {
          formFields.push({
            uuid: 'attachmentFiles',
            key: 'attachmentFiles',
            label: '附件文件列表',
            value: '',
            type: 'files',
            defaultValue: '',
            section: 'attachment',
            required: false,
            show: false // false 隐藏
          })
        }

        // 设置表单字段
        this.formFields = formFields
        // 初始化表单数据
        const formData = {}
        const rules = {}

        // 根据表单字段配置初始化表单数据和校验规则
        this.formFields.forEach(field => {
          // 设置值，优先使用field.value（接口返回的值）
          formData[field.key] = field.value || ''

          // 特殊处理attachmentIds字段，确保为数组
          if (field.uuid === 'attachmentIds') {
            if (typeof formData[field.key] === 'string' && formData[field.key]) {
              formData[field.key] = formData[field.key].split(',').filter(id => id.trim() !== '')
            } else if (!Array.isArray(formData[field.key])) {
              formData[field.key] = []
            }
          }

          // 设置校验规则
          if (
            (field.required ||
              (field.validates && field.validates.length > 0)) &&
            field.show
          ) {
            rules[field.key] = []
            if (field.required) {
              rules[field.key].push(validate.required(field.label))
            }
            if (field.validates && field.validates.length > 0) {
              field.validates.forEach(validateRule => {
                if (validateRule.regular) {
                  rules[field.key].push({
                    pattern: new RegExp(validateRule.pattern),
                    message: validateRule.message,
                    trigger: 'blur'
                  })
                }
              })
            }
          }
        })

        this.formData = formData
        this.rules = rules

        // 初始化需要在表单渲染时就请求的选项数据
        await this.initFieldOptions()
        // 初始化附件列表
        await this.initAttachmentFiles()
        // 设置动态监听器
        this.setupWatchers()
        // 获取并设置电子签名
        await this.getSignature()
      } catch (error) {
        console.error('获取表单配置失败:', error)
      }
    },

    // 获取电子签名
    async getSignature() {
      const res = await getSignatureAttachment()
      this.signatureObj = res.data || {}
      if (res.data && res.data.path) {
        // 查找signatureId字段
        const signatureField = this.formFields.find(
          field => field.uuid === 'signatureId'
        )
        if (signatureField) {
          const path = res.data.path
          picturePreview(path).then(pres => {
            this.formData[signatureField.key] = pres
          })
        }
      }
    },

    // 文件提交
    handleChange(e) {
      const files = e.target.files
      if (!files || !files.length) {
        return
      }
      const size = files[0].size / 1024 / 1024
      const fileType = files[0].type.split('/')[1]
      if (!['jpg', 'jpeg', 'png'].includes(fileType.toLowerCase())) {
        this.$message.error(
          `请上传${this.maxSignatureFileSize}M以内的png/jpg/jpeg文件`
        )
        return
      }
      if (size > this.maxSignatureFileSize) {
        this.$message.error(
          `请上传${this.maxSignatureFileSize}M以内的png/jpg/jpeg文件`
        )
        return
      } else {
        const formData = new FormData()
        formData.append('file', files[0])
        electronicSignatureAttachmentUpload(formData).then(res => {
          if (res.code === 200 || res.code === 0) {
            this.$message({
              message: '上传成功',
              type: 'success'
            })
            this.signatureObj = res.data
            const path = res.data.path
            picturePreview(path).then(res => {
              this.formData[this.uploadName] = res
            })
          }
        })
      }
    },
    // 点击选择文件
    handleClick(name) {
      if (this.endType) {
        return
      }
      this.$refs.fileRef.click()
      this.$refs.fileRef.value = ''

      // 将当前uuid=signatureId的字段key赋值给uploadName
      const signatureField = this.formFields.find(
        field => field.uuid === 'signatureId'
      )
      if (signatureField) {
        this.uploadName = signatureField.key
      }
    },
    clearFileName(ref) {
      if (this.endType) {
        return
      }
      const id = this.signatureObj.id
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteSignatureAttachment(id).then(res => {
            if (res.code === 0 || res.code === 200) {
              this.$message.success('删除成功')
              this.formData[ref] = ''
              this[ref] = ''
            }
          })
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    },
    // 删除附件
    handleRemove(file) {
      if (!file || !file.attachmentId) return

      // 查找附件字段和附件文件字段
      const attachmentField = this.formFields.find(field => field.uuid === 'attachmentIds')
      const attachmentFilesField = this.formFields.find(field => field.uuid === 'attachmentFiles')

      if (!attachmentField || !attachmentFilesField) return

      // 从文件列表中移除该文件
      const fileListIndex = this.fileList1.findIndex(item => item.attachmentId == file.attachmentId)
      if (fileListIndex !== -1) {
        this.fileList1.splice(fileListIndex, 1)
      }

      // 从formData的attachmentIds中移除ID
      if (Array.isArray(this.formData[attachmentField.key])) {
        const idIndex = this.formData[attachmentField.key].indexOf(String(file.attachmentId))
        if (idIndex !== -1) {
          this.formData[attachmentField.key].splice(idIndex, 1)
        }
      }
      // 从attachmentFiles的value中删除文件信息
      if (Array.isArray(attachmentFilesField.value)) {
        const fileIndex = attachmentFilesField.value.findIndex(item => item.id == file.attachmentId)
        if (fileIndex !== -1) {
          attachmentFilesField.value.splice(fileIndex, 1)
        }
      }

      this.$message.success('删除成功')
      this.uploadStatus = 0
    },
    handlePreview1(file, index) {
      this.currentFileIndex1 = index
    },
    handleUpload1(file) {
      if (file.size > 1024 * 1024 * this.maxFileSize) {
        this.$message.error(`上传文件不能超过${this.maxFileSize}MB!`)
        return false
      }
      const promise = new Promise(resolve => {
        this.$nextTick(function() {
          resolve(true)
        })
      })
      return promise
    },
    httpRequest1(file) {
      const formData = new FormData()
      formData.append('file', file.file)
      formData.append('sourceId', this.sourceId)
      formData.append('name', file.file.name)
      this.uploadStatus = 1
      uploadApplicationFile(formData)
        .then(res => {
          if (res.code === 0 || res.code === 200) {
            this.$message({
              message: '上传成功',
              type: 'success'
            })
            this.uploadStatus = 2

            // 查找附件字段和附件文件字段
            const attachmentField = this.formFields.find(field => field.uuid === 'attachmentIds')
            const attachmentFilesField = this.formFields.find(field => field.uuid === 'attachmentFiles')

            if (!attachmentField || !attachmentFilesField) return

            // 确保formData中的attachmentIds字段初始化为数组
            if (!Array.isArray(this.formData[attachmentField.key])) {
              this.formData[attachmentField.key] = []
            }

            // 确保attachmentFiles的value初始化为数组
            if (!Array.isArray(attachmentFilesField.value)) {
              attachmentFilesField.value = []
            }

            // 将返回的附件ID添加到数组中
            if (res.data && res.data.id) {
              const newFileId = String(res.data.id)

              // 检查是否已存在该ID，如果已存在则不添加
              if (!this.formData[attachmentField.key].includes(newFileId)) {
                // 添加附件ID到formData
                this.formData[attachmentField.key].push(newFileId)
              }

              // 准备新文件信息
              const fileInfo = {
                id: newFileId,
                uid: newFileId,
                attachmentId: newFileId,
                name: res.data.name || file.file.name,
                path: res.data.path || '',
                size: res.data.size,
                fileType: res.data.fileType || ''
              }

              // 检查附件文件列表中是否已存在该ID的文件
              const existingFileIndex = attachmentFilesField.value.findIndex(item => item.id === newFileId)
              if (existingFileIndex === -1) {
                // 将文件信息添加到attachmentFiles中
                attachmentFilesField.value.push(fileInfo)
              } else {
                // 更新已存在的文件信息
                attachmentFilesField.value[existingFileIndex] = fileInfo
              }

              // 更新上传成功的文件状态和附件ID
              const fileIndex = this.fileList1.findIndex(
                item =>
                  item.uid === file.file.uid ||
                  (item.raw && item.raw.uid === file.file.uid)
              )

              if (fileIndex !== -1) {
                // 设置文件状态为成功并添加附件ID
                this.$set(this.fileList1[fileIndex], 'status', 'success')
                this.$set(
                  this.fileList1[fileIndex],
                  'attachmentId',
                  newFileId
                )
              } else {
                // 检查文件列表是否已包含该ID的文件
                const existingFile = this.fileList1.find(item => item.attachmentId === newFileId)
                if (!existingFile) {
                  // 如果在文件列表中找不到，添加一个新的文件项
                  this.fileList1.push({
                    name: file.file.name,
                    status: 'success',
                    uid: file.file.uid,
                    id: newFileId,
                    attachmentId: newFileId,
                    size: res.data.size,
                    path: res.data.path // 添加url用于预览
                  })
                }
              }
            }
          } else {
            this.$message({
              message: res.msg || '上传失败',
              type: 'warning'
            })
            this.uploadStatus = 3
          }
        })
        .catch(error => {
          console.error('上传文件失败:', error)
          this.$message({
            message: '上传失败',
            type: 'warning'
          })
          this.uploadStatus = 3
        })
    },

    // 表单提交
    confirm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.loading = true
          // 将formData中的值赋给formFields对应字段的value
          this.formFields.forEach(field => {
            // 保存表单中的值到字段的value中
            field.value = this.formData[field.key]

            // 特殊处理附件，将数组转为逗号分隔的字符串
            if (field.uuid === 'attachmentIds') {
              const arr = []
              this.fileList1.forEach(f => {
                arr.push(f.attachmentId)
              })
              field.value = arr.join(',')
            }

            // 处理电子签名，使用对象ID而不是图片地址
            if (field.uuid === 'signatureId' && this.signatureObj && this.signatureObj.id) {
              field.value = String(this.signatureObj.id) || ''
            }

            // 获取厂商名称并清空options
            if (field.uuid === 'vendorId') {
              const vendorField = this.formFields.find(f => f.uuid === 'vendorId')
              if (vendorField && vendorField.options && vendorField.options.length) {
                const selectedVendor = vendorField.options.find(
                  opt => opt.value === this.formData[field.key]
                )
                if (selectedVendor) {
                  this.vendorName.value = selectedVendor.label || ''
                }
                // 清空options
                field.options = []
              }
            }
            // 获取联系人名称并清空options
            if (field.uuid === 'contactPerson') {
              const contactField = this.formFields.find(f => f.uuid === 'contactPerson')
              if (contactField && contactField.options && contactField.options.length) {
                const selectedContact = contactField.options.find(
                  opt => opt.value === this.formData[field.key]
                )
                if (selectedContact) {
                  this.contactPersonName.value = selectedContact.label || ''
                }
                // 清空options
                field.options = []
              }
            }
          })

          // 处理附件文件列表字段
          const attachmentFilesField = this.formFields.find(field => field.uuid === 'attachmentFiles')
          if (attachmentFilesField) {
            // 将附件文件列表转为JSON字符串
            this.fileList1.length > 0 ? attachmentFilesField.value = JSON.stringify(this.fileList1) : attachmentFilesField.value = ''
          }

          // 构建提交参数 - 使用recordBOList格式，添加厂商名称、联系人名称
          const recordBOList = [
            ...this.formFields,
            this.vendorName,
            this.contactPersonName
          ]

          const params = {
            recordBOList: recordBOList,
            applicationId: this.$route.params.id // 添加ID参数，表示这是编辑模式
          }

          // 调用真实接口
          detectionApplicationUpdate(params)
            .then(res => {
              if (res.code === 0 || res.code === 200) {
                this.$message.success('提交成功')
                this.$router.go(-1)
              } else {
                this.$message.error(res.msg || '提交失败')
              }
            })
            .catch(error => {
              console.error('提交表单失败:', error)
              this.$message.error('提交失败，请稍后重试')
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    },

    searchPackage() {
      // Implementation goes here if needed
      console.log('searchPackage called')
    },

    // 富文本编辑器内容变更处理
    contentChange(key, value) {
      if (this.delHtml(value)) {
        this.formData[key] = value
      } else {
        const imgStrs = value.match(/<img.*?>/g)
        if (imgStrs && imgStrs.length) {
          // 内容只有图片时
          this.formData[key] = value
        } else {
          this.formData[key] = ''
        }
      }
    },

    // 过滤html代码、空格、回车 空白字符
    delHtml(str) {
      str = str.replace(/<("[^"]*"|'[^']*'|[^'">])*>/gi, '')
      str = str.replace(/[\r\n]/g, '')
      str = str.replace(/\s/g, '')
      str = str.replace(/&nbsp;/gi, '')
      return str
    },

    // 初始化附件文件列表
    async initAttachmentFiles() {
      // 查找附件字段和附件文件列表字段
      const attachmentField = this.formFields.find(field => field.uuid === 'attachmentIds')
      const attachmentFilesField = this.formFields.find(field => field.uuid === 'attachmentFiles')

      if (!attachmentField || !attachmentFilesField) return

      // 确保attachmentIds在formData中是数组格式
      if (!Array.isArray(this.formData[attachmentField.key])) {
        if (typeof this.formData[attachmentField.key] === 'string' && this.formData[attachmentField.key]) {
          this.formData[attachmentField.key] = this.formData[attachmentField.key].split(',').filter(id => id.trim() !== '')
        } else {
          this.formData[attachmentField.key] = []
        }
      }

      // 确保attachmentFiles的value是数组格式
      if (!Array.isArray(attachmentFilesField.value)) {
        if (typeof attachmentFilesField.value === 'string' && attachmentFilesField.value) {
          try {
            attachmentFilesField.value = JSON.parse(this.formData[attachmentFilesField.uuid])
            if (!Array.isArray(attachmentFilesField.value)) {
              attachmentFilesField.value = []
            }
          } catch (error) {
            console.error('解析附件文件列表失败:', error)
            attachmentFilesField.value = []
          }
        } else {
          attachmentFilesField.value = []
        }
      }

      // 同步attachmentIds和attachmentFiles，确保两者一致
      const validIds = [...this.formData[attachmentField.key]]

      // 从attachmentFiles中删除不在attachmentIds中的项
      attachmentFilesField.value = attachmentFilesField.value.filter(file =>
        validIds.includes(String(file.attachmentId))
      )
      // 如果有附件文件，则设置文件列表显示
      if (attachmentFilesField.value.length > 0) {
        this.fileList1 = attachmentFilesField.value.map(file => ({
          name: file.name || '未命名文件',
          status: 'success',
          size: file.size,
          attachmentId: file.attachmentId,
          id: file.attachmentId,
          path: file.url || file.path
        }))
      } else {
        this.fileList1 = []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 去掉按 delete 键可删除提示
::v-deep .el-icon-close-tip {
  display: none !important;
}
.img-container {
  position: relative;
  width: 160px;
  height: 80px;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  .delete-btn {
    display: none;
    position: absolute;
    top: 2px;
    right: 2px;
    color: red;
    z-index: 9999;
    cursor: pointer;
  }
}
.img-container:hover {
  .delete-btn {
    display: block;
  }
}
.upload-demo1 {
  position: relative;
  .status-tag {
    position: absolute;
    left: 85px;
    top: 5px;
    text-align: center;
    height: 32px;
    line-height: 32px;
    margin-left: 15px;
    padding: 0 10px !important;
  }
}
.mt-10 {
  margin-top: 10px;
}
.card-header {
  font-weight: bold;
}
.signature-name {
  margin-left: 10px;
  color: #606266;
}
.attachment-item {
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  width: 340px;

  .el-icon-delete {
    color: #f56c6c;
    cursor: pointer;
  }
}
</style>
