<template>
  <div class="dialog-wrap">
    <el-form v-if="isEdit" ref="form" :model="formData" label-position="left" label-width="80px">
      <el-form-item label="姓名">
        <div>{{ formData['name'] || '-' }}</div>
      </el-form-item>
      <el-form-item label="身份证号">
        <div>{{ formData['idNumber'] || '-' }}</div>
      </el-form-item>
      <el-form-item label="企业">
        <div>{{ formData['enterpriseName'] || '-' }}</div>
      </el-form-item>
      <el-form-item label="手机号">
        <div>{{ formData['mobile'] || '-' }}</div>
      </el-form-item>
      <el-form-item label="邮箱">
        <div>{{ formData['mail'] || '-' }}</div>
      </el-form-item>
    </el-form>

    <el-form v-else ref="form" :model="formData" :rules="rules" label-position="left" label-width="80px">
      <el-form-item label="姓名" prop="name">
        <el-input v-model.trim="formData.name" :disabled="isEdit" maxlength="64" show-word-limit />
      </el-form-item>
      <el-form-item label="身份证号" prop="idNumber">
        <el-input v-model.trim="formData.idNumber" :disabled="isEdit" maxlength="64" show-word-limit />
      </el-form-item>
      <el-form-item label="企业" prop="enterprise">
        <template>
          <wk-dep-select
            v-model="formData.enterprise"
            :disabled="isEdit"
            radio
            @change="depChange"
          />
        </template>
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input v-model.trim="formData.mobile" :disabled="isEdit" maxlength="64" show-word-limit />
      </el-form-item>
      <el-form-item v-if="!isEdit" label="密码" prop="password">
        <el-input v-model.trim="formData.password" show-password placeholder="请输入密码"/>
      </el-form-item>
      <el-form-item v-if="!isEdit" label="确认密码" prop="confirmPsd">
        <el-input v-model.trim="formData.confirmPsd" show-password placeholder="请输入密码"/>
      </el-form-item>
      <el-form-item label="邮箱" prop="mail">
        <el-input v-model.trim="formData.mail" :disabled="isEdit" maxlength="64" show-word-limit />
      </el-form-item>
    </el-form>

    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import validate from '@/packages/validate'
import modalMixins from '@/packages/mixins/modal_form'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import WkDepSelect from '@/components/NewCom/WkDepSelect'
import { studentDetail } from '@/api/exam/index.js'

export default {
  components: {
    WkDepSelect
  },
  mixins: [modalMixins, mixinsActionMenu],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    studentDetailId: {
      type: Number
    }
  },
  data() {
    return {
      loading: false,
      validate: validate,
      formData: {
        name: '',
        idNumber: '',
        enterpriseName: '',
        enterprise: '',
        mobile: '',
        password: '',
        confirmPsd: '',
        mail: ''
      },
      rules: {
        name: [validate.required(), validate.base_name],
        idNumber: [validate.required(), validate.idcard],
        enterprise: [validate.required()],
        mobile: [validate.required(), validate.mobilePhone],
        password: [validate.required(), { pattern: /^[\da-zA-Z!@#$%^&*]{1,20}$/, message: '密码由1-20位组成', trigger: 'change' }],
        confirmPsd: [{ required: true, message: '必填项', trigger: 'change' }, { validator: this.validatedConfirmPsd, trigger: 'change' }],
        mail: [validate.required(), validate.email]
      },
      isEdit: true,
      userId: ''
    }
  },
  watch: {
    'isEdit'(newVal, oldVal) {
      this.isEdit ? this.formData.idNumber = this.formData.idNumber.replace(/^(.{6})(?:\d+)(.{4})$/, '\$1******\$2') : this.formData.idNumber
    }
  },
  created() {
    /* 身份证脱敏 */
    const reg = /^(.{6})(?:\d+)(.{4})$/
    this.formData.idNumber = this.formData.idNumber.replace(reg, '\$1******\$2')
    this.getEaxmById()
  },
  methods: {
    validatedConfirmPsd(rule, value, callback) {
      if (value === '') {
        callback(new Error('必填项'))
      } else if (this.formData.password && value != this.formData.password) {
        callback(new Error('密码不一致，请重新输入'))
      } else {
        callback()
      }
    },
    /**
     * 编辑用户单选change
     */
    depChange(_, data) {
      const obj = data && data.length > 0 ? data[0] : null
      this.$set(this.formData, 'parentId', obj ? obj.ownerUserId : '')
    },
    getEaxmById() {
      this.tableLoading = true
      const id = this.studentDetailId
      studentDetail({ id: id }).then((res) => {
        if (res.code === 0) {
          this.formData.name = res.data.realname
          this.formData.idNumber = res.data.idCard
          this.formData.enterprise = res.data.enterpriseCode
          this.formData.enterpriseName = res.data.enterpriseName
          this.formData.mobile = res.data.mobile
          this.formData.mail = res.data.email
          this.userId = res.data.userId
          this.tableLoading = false
        }
      }).catch(() => {
        this.tableLoading = false
      })
    },
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.close()
    }
  }
}
</script>

<style scoped lang="scss">
.wk-dep-select {
  width: 100%;
}
.dialog-wrap {
  .editMsg{
    text-align: right;
    margin-bottom: 20px;
  }
}
</style>
