<template>
  <vertical-list
    :data="labelList"
    :all="false"
    label="label"
    id-name="id"
    children="children"
    selection-mode="first"
    @node-click="nodeClick"
  />
</template>

<script>
import verticalList from '@/packages/vertical-list/index.vue'
import { listLabelTree } from '@/api/admin/label'
export default {
  components: {
    verticalList
  },
  data() {
    return {
      loading: true,
      labelList: [],
      selectItem: []
    }
  },
  created() {
    this.getLabelTree()
  },
  methods: {
    getLabelTree() {
      listLabelTree(this.roleType)
        .then(response => {
          this.labelList = response.data || []
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 点击节点
    nodeClick(node) {
      this.selectItem = [node]
      this.$emit('changeLabel', node)
    }
  }
}
</script>
