<template>
  <div v-loading="loading" class="dialog-wrap">
    <batch-template
      :data="data"
      :available-data="availableData"
      :view-key="viewKey"
      :post-key="postKey"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!availableData.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { setPromotionTeam, setPromotionPlayer } from '@/api/match/index.js'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      viewKey: '',
      postKey: '',
      bigMatchSeasonId: this.$route.query.id || ''
    }
  },
  computed: {
    availableData: function() {
      const tempArr = this.data.filter((item) => {
        return item.promotion != '1'
      })
      return tempArr
    }
  },
  created() {
    if (this.data && this.data.length > 0) {
      this.data.map(item => {
        if (item.teamId) {
          this.viewKey = 'teamName'
          this.postKey = 'teamId'
        }
        if (item.playerId) {
          this.viewKey = 'realName'
          this.postKey = 'playerId'
        }
      })
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const playerIdList = this.availableData.map(item => {
        if (item.playerId) {
          return item.playerId
        }
      })
      const teamIdList = this.availableData.map(item => {
        if (item.teamId) {
          return item.teamId
        }
      })
      const playerData = {
        playerIdList,
        bigMatchSeasonId: this.bigMatchSeasonId
      }
      const teamData = {
        teamIdList,
        bigMatchSeasonId: this.bigMatchSeasonId
      }
      if (this.postKey == 'playerId') {
        setPromotionPlayer(playerData).then(res => {
          if (res.code === 0 || res.code === 200) {
            this.$message.success('设置晋级成功')
            this.$emit('call', 'refresh')
            this.loading = false
            this.close()
          }
        })
      }
      if (this.postKey == 'teamId') {
        setPromotionTeam(teamData).then(res => {
          if (res.code === 0 || res.code === 200) {
            this.$message.success('设置晋级成功')
            this.$emit('call', 'refresh')
            this.loading = false
            this.close()
          }
        })
      }
    }
  }
}
</script>
