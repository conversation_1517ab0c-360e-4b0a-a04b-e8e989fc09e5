<template>
  <create-view
    :loading="loading"
    :show-header="false"
    :show-footer="false"
    title="检测申请详情"
  >
    <div slot="content">
      <el-form
        ref="form"
        :model="formData"
        class="view-details"
        style="padding: 0;"
        label-suffix="："
        label-position="left"
        label-width="140px"
      >
        <el-card>
          <el-divider content-position="left">申请人信息</el-divider>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="申请人">
                <span>{{ applicant || "-" }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="申请人单位">
                <span>{{ applicantUnit || "-" }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="申请时间">
                <span>{{ applicationTime || "-" }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
        <!-- 厂商信息 -->
        <el-card class="mt-10">
          <el-divider content-position="left">厂商信息</el-divider>
          <div v-for="(field, index) in manufacturerFields" :key="`manufacturer-${index}`">
            <el-row :gutter="24">
              <!-- 全宽字段 -->
              <template v-if="field.fullWidth">
                <el-col :span="24">
                  <el-form-item :label="field.fullWidth.label">
                    <span
                      v-if="field.fullWidth.type !== 'richtext'"
                      style="white-space: pre-wrap; word-break: break-all;"
                    >{{ formData[field.fullWidth.key] || "-" }}</span
                    >
                    <div
                      v-else
                      class="richtext-content"
                      style="white-space: pre-wrap; word-break: break-all;"
                      v-html="formData[field.fullWidth.key] || '-'"
                    />
                  </el-form-item>
                </el-col>
              </template>

              <!-- 两列布局字段 -->
              <template v-else>
                <el-col :span="12">
                  <el-form-item :label="field.left.label">
                    <span>{{ formData[field.left.key] || "-" }}</span>
                  </el-form-item>
                </el-col>
                <el-col v-if="field.right" :span="12">
                  <el-form-item :label="field.right.label">
                    <span>{{ formData[field.right.key] || "-" }}</span>
                  </el-form-item>
                </el-col>
              </template>
            </el-row>
          </div>
        </el-card>

        <!-- 产品信息 -->
        <el-card class="mt-10">
          <el-divider content-position="left">产品信息</el-divider>
          <div v-for="(field, index) in productFields" :key="`product-${index}`">
            <el-row :gutter="24">
              <!-- 全宽字段 -->
              <template v-if="field.fullWidth">
                <el-col :span="24">
                  <el-form-item :label="field.fullWidth.label">
                    <span
                      v-if="field.fullWidth.type !== 'richtext'"
                      style="white-space: pre-wrap; word-break: break-all;"
                    >{{ formData[field.fullWidth.key] || "-" }}</span
                    >
                    <div
                      v-else
                      class="richtext-content"
                      style="white-space: pre-wrap; word-break: break-all;"
                      v-html="formData[field.fullWidth.key] || '-'"
                    />
                  </el-form-item>
                </el-col>
              </template>

              <!-- 两列布局字段 -->
              <template v-else>
                <el-col :span="12">
                  <el-form-item :label="field.left.label">
                    <span>{{ formData[field.left.key] || "-" }}</span>
                  </el-form-item>
                </el-col>
                <el-col v-if="field.right" :span="12">
                  <el-form-item :label="field.right.label">
                    <span>{{ formData[field.right.key] || "-" }}</span>
                  </el-form-item>
                </el-col>
              </template>
            </el-row>
          </div>
        </el-card>

        <!-- 备注和附件 -->
        <el-card class="mt-10">
          <el-divider content-position="left">其他信息</el-divider>
          <div v-for="(field, index) in attachmentFields" :key="`attachment-${index}`">
            <el-form-item :label="field.label">
              <!-- 多行文本框 -->
              <span
                v-if="field.type === 'textarea'"
                style="white-space: pre-wrap; word-break: break-all;"
              >
                {{ formData[field.key] || "-" }}
              </span>

              <!-- 富文本 -->
              <div
                v-else-if="field.type === 'richtext'"
                class="richtext-content"
                style="white-space: pre-wrap; word-break: break-all;"
                v-html="formData[field.key] || '-'"
              />

              <!-- 电子签名 -->
              <template v-else-if="field.type === 'signature'">
                <div v-if="formData.signatureUrl" class="img-container">
                  <img :src="formData.signatureUrl" alt="电子签名" >
                </div>
                <span v-else>-</span>
              </template>

              <!-- 附件 -->
              <template v-else-if="field.type === 'file'">
                <div class="attachment-panel">
                  <div
                    v-if="formData.attachments && formData.attachments.length"
                    class="attachment-list"
                  >
                    <div
                      v-for="(item, fileIndex) in formData.attachments"
                      :key="fileIndex"
                      class="attachment-item mb-10"
                    >
                      <i class="el-icon-document" />
                      <div class="attachment-name">{{ item.name }}（{{ (item.size/1024/1024).toFixed(2) }} MB）</div>
                      <div class="attachment-actions">
                        <el-button
                          class="primary"
                          type="text"
                          icon="el-icon-view"
                          @click="handleFileView(item)"
                        />
                        <el-button
                          class="primary"
                          type="text"
                          icon="el-icon-download"
                          @click="handleFileDownload(item)"
                        />
                      </div>
                    </div>
                  </div>
                  <span v-else>-</span>
                </div>
              </template>
            </el-form-item>
          </div>
        </el-card>
      </el-form>
    </div>
  </create-view>
</template>

<script>
import { getSignatureAttachmentById } from '@/api/testing/testingApplication'
import { getApplicationForm } from '@/api/testing/testingApplication.js'
import createView from '@/components/testing/CreateView/index'
import {
  picturePreview
} from '@/packages/utils/downloadAndPreview.js'
import filePreview from '@/components/testing/utils/filePreview'

export default {
  name: 'TestingApplicationDetail',
  components: {
    createView
  },
  mixins: [filePreview],
  data() {
    return {
      loading: false,
      applicant: '',
      applicantUnit: '',
      applicationTime: '',
      formData: {
        signatureUrl: ''
      },
      formFields: [],
      currentSignatureUrl: ''
    }
  },
  computed: {
    // 获取各区域的表单字段并按两列排列处理
    manufacturerFields() {
      return this.getPairedFields('manufacturer')
    },
    productFields() {
      return this.getPairedFields('product')
    },
    attachmentFields() {
      return this.formFields.filter(f => f.section === 'attachment')
    }
  },
  mounted() {
    this.getDetail()
  },
  methods: {
    // 下载文件
    handleFileDownload(item) {
      if (item.path) {
        fetch(item.path, {
          method: 'get',
          responseType: 'blob'
        })
          .then((response) => response.blob())
          .then((blob) => {
            const a = document.createElement('a')
            const URL = window.URL || window.webkitURL
            const herf = URL.createObjectURL(blob)
            a.href = herf
            a.download = item.name
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(herf)
          })
      }
    },
    // 获取两列排列的字段
    getPairedFields(section) {
      const sectionFields = this.formFields.filter(
        f => f.section === section && f.show !== false
      )
      const result = []

      // 富文本和文本框占一行
      const fullWidthFields = sectionFields.filter(
        f => f.type === 'textarea' || f.type === 'richtext' || f.type === 'signature'
      )
      // 其他字段两列排列
      const normalFields = sectionFields.filter(
        f => f.type !== 'textarea' && f.type !== 'richtext' && f.type !== 'signature'
      )

      // 处理常规字段，两个一组
      for (let i = 0; i < normalFields.length; i += 2) {
        const row = {
          left: normalFields[i],
          right: normalFields[i + 1] || null // 可能没有右侧字段
        }
        result.push(row)
      }

      // 处理文本域字段，每个独占一行
      fullWidthFields.forEach(field => {
        result.push({
          fullWidth: field
        })
      })

      return result
    },
    async getDetail() {
      this.loading = true
      try {
        const formRes = await getApplicationForm(this.$route.query.id)
        this.loading = false
        this.applicant = formRes.data.applicant
        this.applicantUnit = formRes.data.applicantUnit
        this.applicationTime = formRes.data.applicationTime

        // 解析表单模板
        const formFieldsTemplate = JSON.parse(formRes.data.template || '[]')
        // 过滤掉show为false的字段
        this.formFields = formFieldsTemplate.filter(field => field.show !== false)

        // 初始化formData为空对象，避免未设置的字段不显示
        this.formData = {
          signatureUrl: ''
        }

        // 处理特殊字段映射 - 注意要从原始formFieldsTemplate中查找，因为show:false的字段已被过滤
        const vendorNameField = formFieldsTemplate.find(
          field => field.uuid === 'vendorName'
        )
        const vendorIdField = formFieldsTemplate.find(
          field => field.uuid === 'vendorId'
        )
        const contactPersonNameField = formFieldsTemplate.find(
          field => field.uuid === 'contactPersonName'
        )
        const contactPersonField = formFieldsTemplate.find(
          field => field.uuid === 'contactPerson'
        )
        const attachmentFilesField = formFieldsTemplate.find(
          field => field.uuid === 'attachmentFiles'
        )
        const signatureField = formFieldsTemplate.find(
          field => field.uuid === 'signatureId'
        )

        // 数据处理
        if (vendorNameField && vendorIdField && vendorIdField.show !== false) {
          vendorIdField.value = vendorNameField.value
        }

        if (contactPersonNameField && contactPersonField && contactPersonField.show !== false) {
          contactPersonField.value = contactPersonNameField.value
        }

        // 处理附件 - 仅在附件字段可见时显示
        const visibleAttachmentField = this.formFields.find(field => field.uuid === 'attachmentIds')
        if (visibleAttachmentField && attachmentFilesField && attachmentFilesField.value) {
          try {
            const attachmentData = JSON.parse(attachmentFilesField.value)
            this.formData.attachments = attachmentData.map(item => ({
              name: item.name,
              path: item.path || item.url || item.fileUrl,
              size: item.size,
              fileType: item.fileType
            }))
          } catch (error) {
            console.error('解析附件数据失败', error)
            this.formData.attachments = []
          }
        } else {
          this.formData.attachments = []
        }

        // 将所有字段的值填充到formData
        formFieldsTemplate.forEach(field => {
          if (field.key && field.value !== undefined) {
            this.formData[field.key] = field.value
          }
        })
        console.log(`🚀🚀🚀 - getDetail - formFieldsTemplate:`, formFieldsTemplate)

        // 处理电子签名 - 移到最后处理，确保formData已经初始化
        if (signatureField && signatureField.value) {
          try {
            const url = await this.getSignature(signatureField.value)
            this.formData.signatureUrl = url
          } catch (error) {
            console.error('获取电子签名失败', error)
          }
        }
      } catch (error) {
        console.error('获取详情失败', error)
        this.loading = false
      }
    },
    getSignature(id) {
      return new Promise((resolve, reject) => {
        getSignatureAttachmentById(id)
          .then(pres => {
            picturePreview(pres.data.path)
              .then(res => {
                this.currentSignatureUrl = res
                resolve(res)
              })
              .catch(reject)
          })
          .catch(reject)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.primary {
  color: var(--color-600);
}
.view-details {
  .el-card.is-always-shadow,
  .el-card.is-hover-shadow:focus,
  .el-card.is-hover-shadow:hover {
    box-shadow: none;
  }
  .el-card__body {
    padding: 6px 20px;
  }
  .el-form-item {
    margin-bottom: 12px;
    word-break: break-all;
    .el-form-item__content,
    .el-form-item__label {
      line-height: 24px;
    }
  }
}
.img-container {
  width: 160px;
  height: 80px;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
.richtext-content{
  ::v-deep img{
    max-width: 100%;
    object-fit: contain;
  }
}
.mt-10 {
  margin-top: 10px;
}
.attachment-list {
  .attachment-item {
    display: flex;
    width: 100%;
    padding: 0 10px;
    align-items: center;
    border: 1px solid var(--color-601-border);
    background: var(--color-601-background);

    i {
      color: #909399;
      margin-right: 5px;
    }

    .attachment-name {
      flex: 1;
      color: #000000D9;
    }

    .attachment-size {
      color: #909399;
      font-size: 12px;
      margin-right: 10px;
    }

    .attachment-actions {
      display: flex;

      .el-button {
        padding: 0 5px;
      }
    }
  }
}
</style>
