<template>
  <div v-loading="loading" class="wrap-layout">
    <detail-view
      :data="data"
      :id="id"
      :view-item="viewItem"
      :params="{
        'id': $route.query.examinationId,
        'view': 'students',
        'examName': $route.query.examName,
        'schedulingCode': $route.query.schedulingCode,
      }"
      :show-header="false"
      style="height: auto;"
      title-key="realname"
    >
      <div slot="action" class="stuMsg">
        <h3>手工阅卷分数: {{ allScore === null ? '-' : allScore }} 分</h3>
      </div>
    </detail-view>
    <div class="content">
      <div v-for="(value, type, index) in questionObj" :key="index + ''">
        <!-- 简答题 -->
        <div v-if="type == 8 && shortAnswerArr.length > 0" class="mb-15">
          <div class="question_types">{{ arabicToChinese(index + 1) }}、{{ questionObj[type].questionTitle }}</div>
          <div v-for="(item, index) in questionObj[type].list" :key="`synthesis${index}`" :id="`qt_synthesis${index}`" class="question_info">
            <div class="flex-space-between ai-start mb-5">
              <div class="comp-question">
                <span>{{ index + 1 }}、&nbsp;</span>
                <span style="max-width: 90%;" v-html="insertNodeInRichText(item.content, ` (${item.questionScore}分)`)"/>
              </div>
              <div style="display: flex;">
                <div class="name_question"><el-button v-if="item.bankType != 1" style="margin-right: 5px;" type="primary" class="env_btn" @click="openEnv(item)">打开实验环境</el-button></div>
                <el-button v-if="!item.Marking" class="env_disabled">未阅卷</el-button>
                <el-button v-else class="env_abled">已阅卷</el-button>
              </div>
            </div>
            <div class="short_answer">
              <div class="stu_answer">
                <div class="flex-space-between">
                  <div style="max-width:85%"><span>学员答案：</span><span> {{ item.questionUserAnswer }}</span></div>
                  <div v-if="isShowEditScore[item.questionCode]">
                    得分:
                    <el-input v-model.trim.number="shortScore[item.questionCode]" style="width: 80px;margin: 0 5px;" size="mini" @change="shortChange(item, shortScore[item.questionCode])"/>
                    分
                  </div>
                  <div v-if="!isShowEditScore[item.questionCode]">
                    <Score :value="shortScore[item.questionCode]" :highlight="false"/>
                    <i style="color: #4b8be0; cursor: pointer;" class="el-icon-edit" @click="editScore(item)"/>
                  </div>
                </div>
              </div>
              <div style="max-width:85%" class="cor_answer"><span>正确答案：</span><span> {{ item.questionAnswer }}</span></div>
              <div style="max-width:85%" class="cor_analysis"><span>题目解析：</span><div style="max-width: 90%;" v-html="item.questionAnalysis"/></div>
            </div>
          </div>
        </div>
        <!-- 组合题 -->
        <div v-if="type == 10 && synthesisArr.length > 0" class="mb-15">
          <div class="question_types">{{ arabicToChinese(index + 1) }}、{{ questionObj[type].questionTitle }}</div>
          <div v-for="(item, index) in questionObj[type].list" :key="`synthesis${index}`" :id="`qt_synthesis${index}`" class="question_info">
            <div class="name_question"><div style="margin-right: 20px;">{{ item.questionName }}</div><el-button type="primary" class="env_btn" @click="openEnv(item)">打开实验环境</el-button></div>
            <div v-for="(q, idx) in item.combinationQuestionBOS" :key="idx" class="border_comp">
              <div class="flex-space-between ai-start mb-5">
                <div class="comp-question">
                  <div>综合题{{ idx + 1 }}.&nbsp;</div>
                  <div style="max-width: 90%;"><span v-html="insertNodeInRichText(q.questionName, ` (${q.questionScore}分)`)" /></div>
                </div>
                <div style="display: flex;">
                  <el-button :disabled="!q.fileUrl" type="primary" class="env_btn" style="margin-right: 5px;" @click="downloadReport(q)">下载WriteUp</el-button>
                  <el-button v-if="!q.Marking" class="env_disabled">未阅卷</el-button>
                  <el-button v-else class="env_abled">已阅卷</el-button>
                </div>
              </div>
              <div v-for="(sub, subIndex) in JSON.parse(q.content)" :key="subIndex" class="comp-content-wrap">
                <div class="flex-1">
                  题目{{ subIndex + 1 }}.&nbsp;
                  <span style="display: block; width: 90%;" v-html="insertNodeInRichText(sub, ` (${q.scoreArr[subIndex]}分)`)"/>
                </div>
                <div>
                  <Score :value="q.synthesisScore[subIndex]" :highlight="false"/>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="footer">
      <el-button :disabled="noPreviousOne" type="primary" @click="up">上一个</el-button>
      <el-button :disabled="downMarking || dowmMarkingShort" type="primary" @click="down">确定并进入下一个</el-button>
    </div>
  </div>
</template>
<script>
import { queryAnswer, submitScoreApi, studentList } from '@/api/exam/index.js'
import detailView from '@/packages/detail-view/index'
import Score from '@/views/admin/hrm/TeachingAffairs/TeachingAffairsExam/components/score.vue'
import { insertNodeInRichText } from '@/components/QuestionBank/utils.js'
export default {
  components: {
    detailView,
    Score
  },
  props: {

  },
  data() {
    return {
      id: null, // 资源ID
      data: {
        name: '阅卷',
        realname: ''
      }, // 资源数据对象
      loading: false,
      viewItem: [],
      name: '',
      score: '',
      synthesisArr: [],
      shortAnswerArr: [],
      studentListData: [],
      studentListParam: {},
      switchStudent: 0,
      maxStudent: 0,
      evaluationCode: this.$route.query.evaluationCode || '-',
      examCode: this.$route.query.examCode || '-',
      examinationId: this.$route.query.examinationId || '-',
      submitType: this.$route.query.submitType || '-',
      currentEvaluationCode: '',
      realname: '',
      markingPapersStatus: 0,
      downMarking: false,
      dowmMarkingShort: false,
      allScore: '-',
      noPreviousOne: false,
      shortScore: {},
      shortMarking: {},
      isShowEditScore: {},
      shortAllScores: null,
      questionObj: {}
    }
  },
  watch: {
    'shortAnswerArr': {
      handler(data) {
        this.dowmMarkingShort = data.some(item => !!item.Marking == false)
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.getStudentList()
  },
  methods: {
    insertNodeInRichText: insertNodeInRichText,
    // 获取参数学生列表
    getStudentList: function() {
      const params = {
        page: 1,
        limit: 10000,
        examStatus: 1,
        examId: this.$route.query.examinationId,
        schedulingCode: this.$route.query.schedulingCode
      }
      studentList(params).then((res) => {
        if (res.code === 0) {
          // 查询参考的学员，过滤掉未提交试卷的
          this.studentListData = (res.data.records || []).filter(item => item.submitExamTime)
          // 阅卷的学生在前面，未阅卷在后面
          this.studentListData.sort((a, b) => b.markingPapersStatus - a.markingPapersStatus)
          this.studentListParam = this.studentListData.find(item => item.evaluationCode == (this.evaluationCode || this.$route.query.evaluationCode))
          this.evaluationCode = this.studentListParam.evaluationCode
          this.data.realname = this.realname = this.studentListParam.realname
          // 该学生是否阅卷 1阅卷  0未阅卷
          this.markingPapersStatus = this.studentListParam.markingPapersStatus
          this.maxStudent = this.studentListData.length
          this.switchStudent = this.studentListData.findIndex(item => item.evaluationCode == this.$route.query.evaluationCode)
          // 如果是第一个学生，上一个不可点
          if (this.switchStudent === 0) {
            this.noPreviousOne = true
          } else {
            const prevStudent = this.studentListData[this.switchStudent - 1]
            // 如果上一个学生未阅卷，禁用上一个按钮
            if (prevStudent.markingPapersStatus == 0) {
              this.noPreviousOne = true
            }
          }
          this.getQuestion()
        }
      })
    },
    shortChange(data, value) {
      value = Number(value)
      if (!value || !Number.isInteger(value) || value < 0) {
        this.$message.warning('请输入自然数')
        this.$set(this.shortScore, `${data.questionCode}`, null)
        return
      }
      if (value > data.questionScore) {
        this.$set(this.shortScore, `${data.questionCode}`, null)
        this.$message.error('所填的分数不能大于该题的分数')
        return
      }
      // 打分有效则关闭打分输入框
      this.$set(this.isShowEditScore, `${data.questionCode}`, false)
      // 计算手工打分总分
      this.shortAllScores = null
      Object.keys(this.shortScore).forEach((key) => {
        if (this.shortScore[key] !== null) {
          this.shortAllScores += this.shortScore[key]
        }
      })
      // 计算综合题分数
      if (this.synthesisArr && this.synthesisArr.length > 0) {
        const allScore = []
        this.synthesisArr.map(item => {
          const scoreListData = JSON.parse(localStorage.getItem('soreList' + item.questionCode))
          // 如果有老师新打的分数就拿新打的分数，否则就拿老师之前打的分数
          if (scoreListData) {
            allScore.push(scoreListData)
          } else {
            allScore.push(JSON.parse(item.combinationUserPoints))
          }
          // 当打分0分时，手工阅卷分数显示0分
          if (this.allScore == 0) {
            this.allScore = '0'
            for (let i = 0; i < localStorage.length; i++) {
              if (localStorage.key(i).includes('soreList')) {
                this.allScore = '0'
              }
            }
          }
        })
        // 老师打的全部题目的总分数
        this.allScore = allScore.flat().flat().reduce((total, score) => {
          if (score !== null) {
            total += score
          }
          return total
        })
        this.allScore = this.getTotal([this.allScore, this.shortAllScores])
      } else {
        this.allScore = this.shortAllScores
      }
      this.shortAnswerArr.map((item) => {
        if (data.questionCode == item.questionCode) {
          item.Marking = true
        }
        this.shortMarking[item.questionCode] = item.Marking
        this.dowmMarkingShort = false
        // 如果题目未阅完，不能点击下个学生
        if (!item.Marking) {
          this.dowmMarkingShort = true
        }
      })
      this.$forceUpdate()
      localStorage.setItem('shortScore', JSON.stringify(this.shortScore))
      localStorage.setItem('shortMarking', JSON.stringify(this.shortMarking))
    },
    editScore(data) {
      this.$set(this.isShowEditScore, `${data.questionCode}`, true) // 显示打分输入框
      this.shortAnswerArr.map((item) => {
        this.dowmMarkingShort = false
        // 如果题目未阅完，不能点击下个学生
        if (!item.Marking) {
          this.dowmMarkingShort = true
        }
      })
    },
    submitScore(lastOne = false) {
      // 简答题参数
      const shortSoreListData = []
      this.shortScore = JSON.parse(localStorage.getItem('shortScore')) || this.shortScore
      for (const key in this.shortScore) {
        if (this.shortScore.hasOwnProperty(key)) { // 确保属性是对象自身的而不是从原型链继承的
          shortSoreListData.push({
            evaluationCode: this.currentEvaluationCode,
            questionCode: key,
            combinationPoints: JSON.stringify([this.shortScore[key]]),
            questionScore: this.shortScore[key]
          })
        }
      }
      const params = this.synthesisArr.map((item) => {
        // 从缓存里面拿老师给学生打的题目分数
        const scoreListData = JSON.parse(localStorage.getItem('soreList' + item.questionCode))
        // 如果有缓存的分数 就拿缓存的分数，没有就拿老师已经阅过卷的分数的
        if (scoreListData) {
          const soreData = this.synthesisArr.find((soreItem) => soreItem.questionCode === item.questionCode)
          const allSore = scoreListData.flat().reduce((a, b) => a + b, 0)
          return {
            evaluationCode: this.currentEvaluationCode,
            questionCode: soreData.questionCode,
            combinationPoints: JSON.stringify(scoreListData),
            questionScore: allSore
          }
        } else {
          const allSore = JSON.parse(item.combinationUserPoints) ? JSON.parse(item.combinationUserPoints).flat().reduce((a, b) => a + b, 0) : ''
          return {
            evaluationCode: this.currentEvaluationCode,
            questionCode: item.questionCode,
            combinationPoints: item.combinationUserPoints,
            questionScore: allSore
          }
        }
      })
      submitScoreApi([...params, ...shortSoreListData]).then((res) => {
        if (res.code === 0) {
          // 在阅卷完最后一个考生并提交后，等接口调用完成才返回考生列表页面
          if (lastOne) {
            this.$message.success('已完成本次考试阅卷')
            this.$router.push({ name: 'detailExamTraining', params: { name: this.$route.query.twoLevelTitle, view: 'students', id: this.$route.query.examinationId, examName: this.$route.query.examName, schedulingCode: this.$route.query.schedulingCode }})
          }
          // 清除题目缓存，为了切换下一个学生是，重置已阅卷和未阅卷状态
          for (let i = 0; i < localStorage.length; i++) {
            if (localStorage.key(i).includes('soreList')) {
              localStorage.removeItem(localStorage.key(i))
            }
          }
          // 切换学生时，该学生是否已经是阅卷状态了
          this.studentListParam = this.studentListData.find(item => item.evaluationCode == (this.evaluationCode || this.$route.query.evaluationCode))
          this.markingPapersStatus = this.studentListParam.markingPapersStatus
          this.getQuestion()
        }
      })
    },
    getQuestion() {
      this.loading = true
      const params = {
        userId: this.studentListParam.userId || this.$route.query.userId,
        examCode: this.examCode,
        evaluationCode: this.evaluationCode,
        examinationId: this.examinationId,
        submitType: this.submitType
      }
      queryAnswer(params).then((res) => {
        if (res.code === 0) {
          this.loading = false
          this.shortAnswerArr = res.data.filter(q => q.questionType == '8') // 简答题
          if (this.shortAnswerArr.length) {
            this.questionObj['8'] = { questionTitle: '简答题', list: this.shortAnswerArr }
          }
          this.shortMarking = JSON.parse(localStorage.getItem('shortMarking')) || {}
          this.shortAnswerArr.map((item) => {
            item.combinationUserPoints = item.combinationUserPoints ? item.combinationUserPoints : '[]'
            this.$set(this.shortScore, `${item.questionCode}`, JSON.parse(item.combinationUserPoints)[0] !== undefined ? JSON.parse(item.combinationUserPoints)[0] : null)
            this.$set(this.isShowEditScore, `${item.questionCode}`, false)
            if (this.markingPapersStatus) {
              item.Marking = true
            }
            if (Object.keys(this.shortMarking).length > 0) {
              for (const key in this.shortMarking) {
                if (key == item.questionCode) {
                  item.Marking = this.shortMarking[key]
                }
              }
            }
          })
          this.synthesisArr = res.data.filter(q => q.questionType == '10')
          if (this.synthesisArr.length) {
            this.questionObj['10'] = { questionTitle: '组合题', list: this.synthesisArr }
          }
          this.synthesisArr.map((item, index) => {
            const scoreData = JSON.parse(item.combinationPoints)
            // 从缓存里面拿老师给学生打的题目分数
            const scoreListData = JSON.parse(localStorage.getItem('soreList' + item.questionCode))
            item.combinationQuestionBOS.map((questionItem, questionIndex) => {
              questionItem.Marking = false
              let rated = false
              const notRatedArr = []
              questionItem.questionScore = scoreData[questionIndex].map(each => Number(each)).reduce((p, q) => p + q)
              questionItem.scoreArr = scoreData[questionIndex] // 子题干的分数
              // 子题干的得分
              if (scoreListData) {
                questionItem.synthesisScore = scoreListData[questionIndex]
                rated = scoreListData[questionIndex].every(score => score !== null) // 分数都不是 null 代表已阅卷
              } else {
                notRatedArr[questionIndex] = Array.from({ length: scoreData[questionIndex].length }, () => null) // 未打分时显示 null
                questionItem.synthesisScore = ((item.combinationUserPoints ? JSON.parse(item.combinationUserPoints) : notRatedArr))[questionIndex]
              }
              // 判断已阅卷和未阅卷的按钮状态  如果已阅过卷或者老师刚阅过卷
              if (this.markingPapersStatus || rated) {
                questionItem.Marking = true
              }
            })
            // 所有子题干都已经阅卷了 可以点击进入下一个
            this.downMarking = !item.combinationQuestionBOS.every(q => q.Marking == true)
            item.questionScore = JSON.parse(item.combinationPoints)[0].map(each => Number(each)).reduce((p, q) => p + q) // 主题干的分数
            // 给每个题目标题家序号
            item.questionName = `组合题${index + 1}.` + item.questionName
          })
          this.shortScore = JSON.parse(localStorage.getItem('shortScore')) || this.shortScore
          this.shortAllScores = null
          Object.keys(this.shortScore).forEach((key) => {
            if (this.shortScore[key] !== null) {
              this.shortAllScores += this.shortScore[key]
            }
          })
          if (this.synthesisArr && this.synthesisArr.length > 0) {
            const allScore = []
            this.synthesisArr.map(item => {
            // this.downMarking = false
            // 如果题目未阅完，不能点击下个学生
            // if (!item.Marking) {
            //   this.downMarking = true
            // }
              const scoreListData = JSON.parse(localStorage.getItem('soreList' + item.questionCode))
              // 如果有老师新打的分数就拿新打的分数，否则就拿老师之前打的分数
              if (scoreListData) {
                allScore.push(scoreListData)
              } else {
                allScore.push(JSON.parse(item.combinationUserPoints))
              }
            })
            // 老师打的全部题目的总分数
            this.allScore = allScore.flat().flat().reduce((total, score) => {
              if (score !== null) {
                total += score
              }
              return total
            })
            this.allScore = this.getTotal([this.allScore, this.shortAllScores])
            // 当打分0分时，手工阅卷分数显示0分
            if (this.allScore == 0) {
              this.allScore = '0'
              for (let i = 0; i < localStorage.length; i++) {
                if (localStorage.key(i).includes('soreList')) {
                  this.allScore = '0'
                }
              }
            }
          } else {
            this.allScore = this.shortAllScores
          }
        }
      })
    },
    // 累加计算方法
    getTotal(arr) {
      const result = arr.reduce((total, score) => {
        if (score !== null) {
          total += score
        }
        return total
      })
      return result
    },
    up() {
      // 如果是第一个学生不可点击上一个
      this.noPreviousOne = false
      if (this.switchStudent === 1) {
        this.noPreviousOne = true
      }
      this.currentEvaluationCode = this.studentListData[this.switchStudent].evaluationCode
      this.switchStudent = this.switchStudent - 1
      if (this.switchStudent < 0) {
        this.switchStudent = 0
        return
      }
      // 切换是，修改学生姓名和请求参数
      this.evaluationCode = this.studentListData[this.switchStudent].evaluationCode
      this.data.realname = this.realname = this.studentListData[this.switchStudent].realname
      // 如果当前学员未完成阅卷不需要调接口保存
      if (this.downMarking) {
        this.studentListParam = this.studentListData.find(item => item.evaluationCode == (this.evaluationCode || this.$route.query.evaluationCode))
        this.markingPapersStatus = this.studentListParam.markingPapersStatus
        this.getQuestion()
      } else {
        this.submitScore()
      }
    },
    down() {
      localStorage.removeItem('shortMarking')
      localStorage.removeItem('shortScore')
      this.currentEvaluationCode = this.studentListData[this.switchStudent].evaluationCode
      this.switchStudent = this.switchStudent + 1
      // 最后一个时，不可点击了
      if (this.switchStudent >= this.maxStudent) {
        this.switchStudent = this.maxStudent - 1
        this.submitScore(true)
        return
      }
      this.noPreviousOne = false
      this.evaluationCode = this.studentListData[this.switchStudent].evaluationCode
      this.data.realname = this.realname = this.studentListData[this.switchStudent].realname
      this.submitScore()
    },
    openEnv(data) {
      this.$router.push({
        name: 'openEnvTraining',
        query: {
          ...data,
          ...this.$route.query,
          evaluationCode: this.evaluationCode
        }
      })
    },
    // 下载文件
    downloadReport(item) {
      if (item.fileUrl) {
        fetch(item.fileUrl, {
          method: 'get',
          responseType: 'blob'
        })
          .then((response) => response.blob())
          .then((blob) => {
            const a = document.createElement('a')
            const URL = window.URL || window.webkitURL
            const herf = URL.createObjectURL(blob)
            a.href = herf
            a.download = item.fileName
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(herf)
          })
      }
    },
    arabicToChinese(number) {
      const chineseDigits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      const chineseUnits = ['', '十', '百', '千', '万', '亿']
      if (number === 0) {
        return chineseDigits[0]
      }
      let chineseNumber = ''
      let unitIndex = 0
      while (number > 0) {
        const digit = number % 10
        if (digit !== 0) {
          chineseNumber = chineseDigits[digit] + chineseUnits[unitIndex] + chineseNumber
        } else {
          if (chineseNumber[0] !== chineseDigits[0]) {
            chineseNumber = chineseDigits[digit] + chineseNumber
          }
        }
        number = Math.floor(number / 10)
        unitIndex++
      }
      return chineseNumber
    }
  }
}
</script>

<style scoped lang="scss">
.question_info {
  margin: 20px 10px;
    padding: 10px;
    font-size: 14px;
    border: 1px solid #d1d1d1;
    border-radius: 10px;
    box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
  .short_answer {
    padding-left: 25px;
    .stu_answer {

    }
    .cor_answer {
      margin: 10px 0;
    }
    .cor_analysis {
      display: flex;
    }
  }
}
.footer {
  width: 100%;
  text-align: right;
  padding: 10px 24px;
  border-top: 1px solid var(--neutral-300);
  background-color: var(--neutral-0);
}
.name_question {
  display: flex;
  justify-content: space-between;
  font-weight: 700;
  font-size: 15px;
}
.border_comp {
  border: 1px solid #d1d1d1;
  border-radius: 10px;
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
  margin: 10px;
  padding: 10px;
}
.wrap-layout {
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 880px;
  height: 100%;
  // overflow-y: auto;
  /deep/ .detail-tabs {
    display: none;
  }
}
  .question_types {
    font-size: 16px;
    font-weight: 600;
    height: 35px;
    line-height: 35px;
    font-family: Source Han Sans CN;
  }
  .comp-question {
    flex: 1;
    display: flex;
    max-height: 200px;
    overflow-y: auto;
    font-weight: 600;
    font-size: 14px;
    color: rgb(36, 41, 47);
    >span {
      word-break: break-all;
    }
  }
  .comp-content-wrap {
    display: flex;
    justify-content: space-between;
    border-radius: 10px;
    border: 1px solid #e5e6eb;
    margin: 5px 0 10px;
    padding: 10px 20px;
    >div:first-child {
      display: flex;
      max-height: 200px;
      overflow-y: auto;
      margin-bottom: 10px;
      >span {
        word-break: break-all;
      }
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  .env_btn {
  width: 90px;
  min-width: 90px;
  font-size: 13px;
  text-align: center;
  border: none;
  background-color: var(--color-600);
  color: #ffffff;
  padding: 0;
}
.env_disabled {
    background-color: #f7f7f7;
    border: none;
    color: #999999;
}
.env_abled {
  background-color: #88cf65;
    border: none;
    color: #ffffff;
}
  .stuMsg {
    display: flex;
    justify-content: space-between;
    padding: 0px 20px;
  }
  .content {
    flex: 1;
    overflow-y: auto;
    padding: 0px 20px;
    .title {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 10px 0;
    }
  }
  ._question_info{
    position: relative;
    .download {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  /deep/.detail-wrap-layout {
    .detail-header {
      // margin-top: 45px;
    }
    .detail-breadcrumb {
      width: 100%;
      background: #ffffff;
    }
    .detail-breadcrumb::after {
      display: none;
      content: ""; /* 必须要有内容，才能显示伪元素 */
      position: absolute; /* 绝对定位，以便位于 div 元素下面 */
      bottom: 3px; /* 控制伪元素的位置，使其在 div 元素下面 */
      left: 0;
      width: 100%;
      height: 1px; /* 阴影的高度 */
      box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.8);
    }
  }
</style>
