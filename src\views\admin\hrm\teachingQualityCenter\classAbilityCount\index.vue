<template>
  <div class="content-wrap-layout">
    <top-nav />
    <div class="class-wrap-layout">
      <el-row :gutter="15" style="height: 100%;">
        <el-col :span="4" style="height: 100%;">
          <el-card style="height: 100%;">
            <div>
              <span>专业:</span>
              <el-select v-model="majorCode" class="majorCode" size="small" placeholder="请选择" @change="handleMajorChange">
                <el-option
                  v-for="item in majorOptions"
                  :key="item.majorCode"
                  :label="item.majorName"
                  :value="item.majorCode"
                />
              </el-select>
            </div>
            <el-input
              v-model.trim="className" size="small" class="mt-15 mb-15" maxlength="64" suffix-icon="el-icon-search" clearable placeholder="请输入班级名称" @clear="getClassByMajorId" @blur="getClassByMajorId" @click="getClassByMajorId"
              @keyup.enter.native="getClassByMajorId" />
            <div v-loading="classLoading" class="class-wrap">
              <div v-if="classList.length === 0" class="class-empty">暂无班级</div>
              <div v-overflow-tooltip v-for="(item, index) in classList" :key="item.majorCode" :class="{ 'active': index === activeIndex }" class="class-div" @click="handleSelectClass(item.majorCode, index)">
                <el-tooltip
                  :content="item.majorName"
                  effect="dark"
                  placement="top">
                  {{ item.majorName }}
                </el-tooltip>
                {{ item.majorName }}
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="20">
          <el-card>
            <div class="top-pie-wrap">
              <div class="pie-left">
                <div class="pie-title">班级能力统计</div>
                <div id="pie" ref="pieWrap" class="pie-chart"/>
              </div>
              <div class="right-type">
                <div class="type-div">
                  <div class="flex ai-center">
                    <div class="square" style="background: #4080FF;" />
                    <div>课程</div>
                  </div>
                  <p v-for="(item, index) in classAbilityData.kcList" :key="index" :title="item" class="mt-5">{{ item }}</p>
                  <p v-if="Array.isArray(classAbilityData.kcList) && classAbilityData.kcList.length === 0" class="mt-5 text-center">暂无数据</p>
                </div>
                <div class="type-div">
                  <div class="flex ai-center">
                    <div class="square" style="background: #FADC19;" />
                    <span>模拟练习</span>
                  </div>
                  <p v-for="(item, index) in classAbilityData.ksList" :key="index" :title="item" class="mt-5">{{ item }}</p>
                  <p v-if="Array.isArray(classAbilityData.ksList) && classAbilityData.ksList.length === 0" class="mt-5 text-center">暂无数据</p>
                </div>
                <div class="type-div">
                  <div class="flex ai-center">
                    <div class="square" style="background: #2BD7B3;" />
                    <span>项目</span>
                  </div>
                  <p v-for="(item, index) in classAbilityData.xmList" :key="index" :title="item" class="mt-5">{{ item }}</p>
                  <p v-if="Array.isArray(classAbilityData.xmList) && classAbilityData.xmList.length === 0" class="mt-5 text-center">暂无数据</p>
                </div>
                <div class="type-div">
                  <div class="flex ai-center">
                    <div class="square" style="background: #F76560;" />
                    <span>教案</span>
                  </div>
                  <p v-for="(item, index) in classAbilityData.janList" :key="index" :title="item" class="mt-5">{{ item }}</p>
                  <p v-if="Array.isArray(classAbilityData.janList) && classAbilityData.janList.length === 0" class="mt-5 text-center">暂无数据</p>
                </div>
              </div>
            </div>
          </el-card>
          <el-row :gutter="15" class="mt-15">
            <!-- 课程成绩 -->
            <el-col :span="8">
              <el-card>
                <div>学员课程成绩排名</div>
                <div v-if="courseRankList.length" class="course-rank">
                  <ul>
                    <li class="ul-head">
                      <span>排名</span>
                      <span>姓名</span>
                      <span>总分数</span>
                    </li>
                    <div>
                      <li v-for="(item, index) in courseRankList" :key="index">
                        <span :style="getRankRowColor(item.rank)">
                          <img v-if="item.rank == 1" src="@/assets/ranking/first.png" alt="" class="leader person-image-style">
                          <img v-else-if="item.rank == 2" src="@/assets/ranking/second.png" alt="" class="leader person-image-style">
                          <img v-else-if="item.rank == 3" src="@/assets/ranking/third.png" alt="" class="leader person-image-style">
                          <span v-else>{{ item.rank }}</span>
                        </span>
                        <span :style="getRankRowColor(item.rank)">{{ item.realname }}</span>
                        <span :style="getRankRowColor(item.rank)">{{ item.score }}</span>
                      </li>
                    </div>
                  </ul>
                </div>
                <el-empty v-else :image="img" :image-size="180" class="course-rank flex-1" description="暂无数据"/>
              </el-card>
            </el-col>
            <!-- 模拟练习成绩 -->
            <el-col :span="8">
              <el-card>
                <div>学员模拟练习成绩排名</div>
                <div v-if="testRankList.length" class="course-rank">
                  <ul>
                    <li class="ul-head">
                      <span>排名</span>
                      <span>姓名</span>
                      <span>总分数</span>
                    </li>
                    <div>
                      <li v-for="(item, index) in testRankList" :key="index">
                        <span :style="getRankRowColor(item.rank)">
                          <img v-if="item.rank == 1" src="@/assets/ranking/first.png" alt="" class="leader person-image-style">
                          <img v-else-if="item.rank == 2" src="@/assets/ranking/second.png" alt="" class="leader person-image-style">
                          <img v-else-if="item.rank == 3" src="@/assets/ranking/third.png" alt="" class="leader person-image-style">
                          <span v-else>{{ item.rank }}</span>
                        </span>
                        <span :style="getRankRowColor(item.rank)">{{ item.realname }}</span>
                        <span :style="getRankRowColor(item.rank)">{{ item.score }}</span>
                      </li>
                    </div>
                  </ul>
                </div>
                <el-empty v-else :image="img" :image-size="180" class="course-rank flex-1" description="暂无数据"/>
              </el-card>
            </el-col>
            <!-- 项目实训成绩 -->
            <el-col :span="8">
              <el-card>
                <div>学员项目实训成绩排名</div>
                <div v-if="projectRankList.length" class="course-rank">
                  <ul>
                    <li class="ul-head">
                      <span>排名</span>
                      <span>姓名</span>
                      <span>总分数</span>
                    </li>
                    <div>
                      <li v-for="(item, index) in projectRankList" :key="index">
                        <span :style="getRankRowColor(item.rank)">
                          <img v-if="item.rank == 1" src="@/assets/ranking/first.png" alt="" class="leader person-image-style">
                          <img v-else-if="item.rank == 2" src="@/assets/ranking/second.png" alt="" class="leader person-image-style">
                          <img v-else-if="item.rank == 3" src="@/assets/ranking/third.png" alt="" class="leader person-image-style">
                          <span v-else>{{ item.rank }}</span>
                        </span>
                        <span :style="getRankRowColor(item.rank)">{{ item.realname }}</span>
                        <span :style="getRankRowColor(item.rank)">{{ item.score }}</span>
                      </li>
                    </div>
                  </ul>
                </div>
                <el-empty v-else :image="img" :image-size="180" class="course-rank flex-1" description="暂无数据"/>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import moduleConf from './config'
import topNav from '../index_top_nav'
import * as echarts from 'echarts'
import { queryClassByMajorId } from '@/api/admin/training/class'
import { queryMajor } from '@/api/admin/training/student'
import { classAbilityStatistics, studentCourseRanking, studentTestRanking, studentProjectRanking } from '@/api/admin/training/teachingQuality'

export default {
  components: {
    topNav
  },
  data() {
    return {
      img: require('@/packages/table-view/nodata.png'), // 暂无数据图片
      moduleName: moduleConf.name,
      majorCode: '',
      classCode: '',
      majorOptions: [],
      className: '',
      classAbilityData: {},
      courseRankList: [],
      testRankList: [],
      projectRankList: [],
      classList: [],
      activeIndex: 0,
      classLoading: false
    }
  },
  mounted() {
    this.getMajorTree()
    this.handleMajorTooltip()
  },
  methods: {
    // 班级模糊查询
    getClassByMajorId() {
      const params = {
        majorCode: this.majorCode,
        className: this.className,
        isAdmin: 1
      }
      queryClassByMajorId(params).then((res) => {
        if (res.code === 0 && res.data) {
          this.classList = res.data
        }
      })
    },
    // 获取专业树
    getMajorTree() {
      queryMajor().then((res) => {
        if (res.code === 0) {
          this.majorOptions = res.data
          this.majorCode = res.data[0].majorCode
          this.handleMajorChange(this.majorCode)
        }
      })
    },
    async handleMajorTooltip() {
      await this.$nextTick()
      const majorSelect = document.querySelector('.majorCode .el-input__inner')
      majorSelect && (majorSelect.title = majorSelect.value)
    },
    // 切换专业
    handleMajorChange(majorCode) {
      this.classLoading = true
      const params = {
        majorCode: majorCode,
        isAdmin: 1
      }
      queryClassByMajorId(params).then((res) => {
        if (res.code === 0) {
          if (res.data && res.data.length) {
            this.classList = res.data
            this.classCode = this.classList[0].majorCode
            this.handleSelectClass(this.classCode, 0)
          } else {
            this.handleSelectClass(0, 0)
          }
          this.classLoading = false
        }
      })
    },
    // 选择班级
    handleSelectClass(code, index) {
      this.activeIndex = index
      Object.keys(this.classAbilityData).forEach(key => {
        delete this.classAbilityData[key]
      })
      classAbilityStatistics({ classCode: code }).then((res) => {
        if (res.code === 0 && res.data) {
          this.classAbilityData = res.data
          this.initClassAbility(this.classAbilityData)
        } else {
          this.initClassAbility({})
        }
      })
      this.getCourseRanking(code)
      this.getTestRanking(code)
      this.getProjectRanking(code)
      this.handleMajorTooltip()
    },
    // 获取课程排名
    getCourseRanking(code) {
      studentCourseRanking({ classCode: code }).then((res) => {
        if (res.code === 0) {
          this.courseRankList = res.data || []
          this.courseRankList.map((item, index) => {
            item.rank = index + 1
          })
        }
      })
    },
    // 获取模拟练习排名
    getTestRanking(code) {
      studentTestRanking({ classCode: code }).then((res) => {
        this.testRankList = res.data || []
        this.testRankList.map((item, index) => {
          item.rank = index + 1
        })
      })
    },
    // 获取项目排名
    getProjectRanking(code) {
      studentProjectRanking({ classCode: code }).then((res) => {
        this.projectRankList = res.data || []
        this.projectRankList.map((item, index) => {
          item.rank = index + 1
        })
      })
    },
    // 获取排名行文字颜色
    getRankRowColor(rank) {
      let rowColor = ''
      if (rank == '1') {
        rowColor = '#FFBB33'
      } else if (rank == '2') {
        rowColor = '#B2C9D9'
      } else if (rank == '3') {
        rowColor = '#D3914F'
      } else {
        rowColor = '#333333'
      }
      return { color: rowColor }
    },
    // 班级能力
    initClassAbility(classData) {
      let myPie = null
      if (!this.$refs.pieWrap) {
        return
      }
      myPie = echarts.init(this.$refs.pieWrap)
      let data = []
      let totalNum = 0
      if (Object.keys(classData).length != 0) {
        data = [
          { name: '课程', value: this.classAbilityData.kc, itemStyle: { color: '#4080FF' }},
          { name: '模拟练习', value: this.classAbilityData.ks, itemStyle: { color: '#FADC19' }},
          { name: '项目', value: this.classAbilityData.xm, itemStyle: { color: '#2BD7B3' }},
          { name: '教案', value: this.classAbilityData.jan, itemStyle: { color: '#F76560' }}
        ]
        totalNum = data.reduce((acc, cur) => acc + cur.value, 0)
      } else {
        data = [
          { name: '课程', value: 0, itemStyle: { color: '#BEDAFF' }},
          { name: '模拟练习', value: 0, itemStyle: { color: '#BEDAFF' }},
          { name: '项目', value: 0, itemStyle: { color: '#BEDAFF' }},
          { name: '教案', value: 0, itemStyle: { color: '#BEDAFF' }},
          { name: '', value: 1, itemStyle: { color: '#BEDAFF' }}
        ]
      }
      myPie.setOption({
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            if (params.name == '') {
              return ''
            }
            const percent = ((Number(params.value) / totalNum) * 100).toFixed(1)
            return params.name + ': ' + percent + '%'
          }
        },
        legend: {
          bottom: 0,
          left: '23%',
          itemWidth: 4,
          itemHeight: 35,
          itemGap: 18,
          data: ['课程', '模拟练习', '项目', '教案'],
          formatter: function(name) {
            let percent = 0
            for (let i = 0; i < data.length; i++) {
              if (data[i].name == name) {
                if (data[i].value) {
                  percent = ((Number(data[i].value) / totalNum) * 100).toFixed(1)
                } else {
                  percent = data[i].value.toFixed(1)
                }
              }
            }
            return `{a|${name}}\n${percent + '%'}`
          },
          textStyle: {
            rich: {
              a: {
                fontWeight: 400,
                color: '#333',
                lineHeight: 20,
                margin: [0, 0, 15, 0]
              }
            }
          }
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['40%', '40%'],
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false
              }
            },
            labelLine: {
              show: false
            },
            data: data
          }
        ]
      })
      window.onresize = function() {
        myPie.resize()
      }
      // legend至少保留一个图例，避免饼图全部消失
      myPie.on('legendselectchanged', function(params) {
        var optionLegend = myPie.getOption()
        var select_value = Object.values(params.selected)
        var n = 0
        select_value.map(function(res) {
          if (!res) {
            n++
          }
        })
        if (n == select_value.length) {
          optionLegend.legend[0].selected[params.name] = true
        }
        myPie.setOption(optionLegend)
      })
    }
  }
}
</script>
<style lang="scss">
.class-wrap-layout {
  margin: 20px;
  height: 100%;
  font-family: Source Han Sans CN;
  font-size: 16px;
  color: #333;
  .majorCode {
    flex: 1;
    .el-input__inner {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .class-wrap {
    background: #fafafa;
    max-height: calc(100% - 135px);
    overflow-y: auto;
    .class-div {
      font-size: 14px;
      padding: 0 15px;
      height: 36px;
      line-height: 36px;
      cursor: pointer;
      max-width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .class-empty {
      height: 60px;
      line-height: 60px;
      text-align: center;
    }
    .active {
      background: var(--color-50);
      color: var(--color-600);
    }
  }
  .top-pie-wrap {
    height: 300px;
    display: flex;
    justify-content: flex-start;
    .pie-left {
      width: 40%;
      .pie-chart {
        height: 260px;
      }
    }
    .right-type {
      display: flex;
      width: 60%;
      padding-top: 30px;
      .type-div {
        flex: 1;
        margin-right: 5px;
        padding: 0 5px 0 5px;
        max-height: 260px;
        overflow-y: auto;
        p {
          max-width: 200px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .square {
          width: 20px;
          height: 20px;
          margin-right: 10px;
        }
      }
    }
  }
  .course-rank {
    height: 230px;
    margin-top: 10px;
    ul {
      width: 100%;
      height: 100%;
      padding: 0;
      margin: 0;
      overflow-x: hidden;
      overflow-y: auto;
      .ul-head {
        font-weight: 600;
      }
      li {
        outline: none;
        list-style: none;
        display: flex;
        width: 99%;
        height: 32px;
        font-size: 14px;
        margin-bottom: 3px;
        font-weight: 400;
        span {
          display: flex;
          width: 100%;
          height: 100%;
          justify-content: center;
          &:nth-child(2) {
            width: 100%;
            justify-content: center;
          }
          &:first-child {
            width: 50%;
            justify-content: center;
          }
          &:last-child {
            width: 50%;
            justify-content: center;
          }
        }
      }
    }
  }
}
</style>
