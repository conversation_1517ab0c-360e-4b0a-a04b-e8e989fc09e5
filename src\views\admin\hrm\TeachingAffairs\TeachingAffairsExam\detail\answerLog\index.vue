<template>
  <div class="content-wrap-layout">
    <!-- 分类区 -->
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      :filter-data="{}"
      default-selected-key="examCode"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
      @emitSearchName="emitSearchName"
    >
      <action-menu
        slot="action"
        :module-name="moduleName"
        :select-item="selectItem"
        :search-name="searchName"
        @call="actionHandler"
      />
    </page-table>
    <el-drawer
      :visible.sync="detailShow"
      :modal="false"
      size="75%"
      append-to-body
      class="detail-view"
      @close="closeDetail"
    >
      <t-detail v-if="detailShow" />
    </el-drawer>
  </div>
</template>

<script>
import moduleConf from './config'
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
import tDetail from './detail/index.vue'
import moduleMixin from '@/packages/mixins/module_list'

export default {
  name: 'Students',
  components: {
    pageTable,
    actionMenu,
    tDetail
  },
  mixins: [moduleMixin],
  data() {
    return {
      detailShowOfName: ['answerRecordTraining'],
      listRouterName: 'detailExamTraining',
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      searchName: {}
    }
  },
  computed: {
    listRouterParams() {
      return {
        id: this.$route.params.examId,
        view: 'answerLog',
        examName: this.$route.params.examName,
        schedulingCode: this.$route.params.schedulingCode
      }
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.params.hasOwnProperty('schedulingCode') ? to.params.schedulingCode : null
      const fromId = from.params.hasOwnProperty('schedulingCode') ? from.params.schedulingCode : null
      if (toId !== fromId) {
        this.$refs['table'].getList()
      }
    }
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    emitSearchName(val) {
      this.searchName = val
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.selectItem = []
          this.$refs['table'].getList(false)
      }
    },
    refresh: function() {}
  }
}
</script>

<style scoped lang="scss">
.content-wrap-layout {
  padding: 0 !important;
}
</style>
