<template>
  <div class="buttons-wrap">
    <!-- <el-button slot="action" :disabled="singleDisabled" type="primary" @click="clickDrop('cloneProgram')">克隆到我的教案</el-button> -->
    <!-- 弹窗 -->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
export default {
  components: {
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
      },
      confirmDisabled: false
    }
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.buttons-wrap{
  margin-left: -5px;
}
</style>
