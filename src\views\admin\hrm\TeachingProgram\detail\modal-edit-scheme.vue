<template>
  <div v-loading="loading" class="dialog-wrap">
    <div class="dialog-wrap-content">
      <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="80px">
        <el-form-item label="名称:" prop="name">
          <el-input v-model.trim="formData.name" maxlength="64" show-word-limit placeholder="请输入内容,1-64个字符以内"/>
        </el-form-item>
      </el-form>
    </div>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import validate from '@/packages/validate'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import { lessonPlanHierarchyUpdate, lessonPlanDetailQuery } from '@/api/teacher/index.js'
export default {
  components: {
  },
  mixins: [mixinsActionMenu],
  props: {
    // 传入数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      validate: validate,
      formData: {
        name: ''
      },
      rules: {
        name: [validate.required(), validate.base_name]
      },
      treeData: [],
      addItemId: ''
    }
  },
  created() {
    this.formData.name = this.data[0].name
    this.detailQuery()
  },
  methods: {
    // 获取方案详情
    detailQuery() {
      const params = {
        lessonPlanId: this.$route.query.id
      }
      lessonPlanDetailQuery(params).then(res => {
        this.treeData = [res.data]
        this.treeData.forEach(item => {
          item.type = 0
          if (item.children) {
            item.children.forEach(it => {
              it.type = 1
              if (it.children) {
                it.children.forEach(i => {
                  i.type = 2
                })
              }
            })
          }
        })
        this.filterAddItem()
      })
    },
    // 查找新增体系节点
    filterAddItem() {
      const addItem = this.treeData[0].children.filter(item => {
        return item.name === this.formData.name
      })
      this.addItemId = addItem[0].id
    },
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const params = {
            hierarchyName: this.formData.name,
            hierarchyId: this.addItemId,
            lessonPlanId: this.$route.query.id
          }
          lessonPlanHierarchyUpdate(params).then((res) => {
            if (res.code === 0) {
              this.$message({
                message: '编辑成功',
                type: 'success'
              })
              this.data[0].name = this.formData.name // 实时刷新节点名称
              this.$emit('call', 'refresh')
              this.close()
            }
          }).catch(() => {
            this.loading = false
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>


