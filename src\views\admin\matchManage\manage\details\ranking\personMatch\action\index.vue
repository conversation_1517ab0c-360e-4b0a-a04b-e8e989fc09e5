<template>
  <div class="buttons-wrap">
    <el-button type="primary" @click="clickDrop('exportRanking')">导出</el-button>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import exportRanking from './modal-export'

export default {
  components: {
    exportRanking
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'exportRanking': '导出'
      },
      dialogLoading: false
    }
  },
  mounted() {
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      } else if (type === 'setLoading') {
        this.dialogLoading = data
      }
    },
    clickDrop(name) {
      if (this.drawerAction && this.drawerAction.includes(name)) {
        this.drawerName = name
      } else {
        this.modalName = name
      }
    }
  }
}
</script>
