const behaviorResultArr = [
  { value: '1', label: '成功', type: 'success' },
  { value: '0', label: '失败', type: 'danger' }
]

export const searchKeyList = [
  { key: 'realname', label: '用户', master: true },
  {
    key: 'time_range',
    label: '操作时间',
    format: 'yyyy-MM-dd HH:mm:ss',
    type: 'time_range',
    placeholder: '选择时间范围'
  },
  { key: 'object', label: '对象名称' },
  { key: 'objectId', label: '对象 id' },
  { key: 'subModel', label: '任务' },
  {
    key: 'behaviorResult',
    label: '操作结果',
    type: 'radio',
    valueList: [
      ...behaviorResultArr
    ]
  }
]
export const columnsObj = {
  'realname': { title: '用户', master: true },
  'createTime': { title: '操作时间' },
  'subModel': { title: '任务' },
  'object': { title: '对象' },
  'objectId': { title: '对象ID' },
  'detail': { title: '详情' },
  'ipAddress': { title: '客户端IP地址' },
  'behaviorResult': { title: '操作结果' }
}
export const columnsViewArr = Object.keys(columnsObj)

// 操作结果
export const behaviorResultObj = behaviorResultArr.reduce((acc, prev) => {
  acc[prev.value] = prev
  return acc
}, {})
