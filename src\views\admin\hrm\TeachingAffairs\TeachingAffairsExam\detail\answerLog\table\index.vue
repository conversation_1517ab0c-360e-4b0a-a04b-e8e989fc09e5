<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索考生姓名"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      type="list"
      current-key="userId"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >考生管理
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'realname'">
            <a
              v-if="link"
              href="javascript:;"
              @click.stop="linkEvent('answerRecordTraining', scope.row,{
                id: scope.row.id,
                realname: scope.row.realname,
                userId: scope.row.userId,
                examId: $route.params.id,
                examName: $route.params.examName,
                examCode: scope.row.examCode,
                evaluationCode: scope.row.evaluationCode,
                examStatus: scope.row.examStatus,
                schedulingCode: $route.params.schedulingCode,
                score: scope.row.score,
                view: 'questionInfo'})">{{ scope.row.realname }}
            </a>
          </span>
          <span v-else>
            <span v-if="scope.row[item] == 0">{{ scope.row[item] }}</span>
            <span v-else>{{ scope.row[item] || "-" }}</span>
          </span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { studentList } from '@/api/exam/index.js'
import { getAccRaceRanking } from '@/utils/acc.js'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      titleMapping: {
      },
      // 搜索配置项
      searchKeyList: [
        { key: 'realname', label: '考生姓名', master: true, placeholder: '默认搜索考生姓名' }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: module.columnsObj,
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: module.columnsViewArr,
      status: this.$route.params.examStatus
    }
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    getList: function() {
      this.tableLoading = true
      const data = this.getPostData('page', 'limit')
      const params = {
        ...data,
        examId: this.$route.params.id,
        schedulingCode: this.$route.params.schedulingCode
      }
      this.$emit('emitSearchName', params)
      studentList(params).then((res) => {
        if (res.code === 0) {
          this.tableData = res.data.records
          // 按分数从高到低排序
          if (this.tableData && this.tableData.length > 0) {
            getAccRaceRanking(this.tableData)
          }
          this.tableTotal = res.data.total
          this.tableLoading = false
        }
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
