<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length" :data="columnsObj" :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol" />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView" :search-key-list="searchKeyListView" default-placeholder="默认搜索学生姓名"
      @search="searchMultiple" />
    <!-- 列表 -->
    <t-table-view
      ref="tableView" :height="height" :single="single" :loading="tableLoading" :data="tableData"
      :total="tableTotal" :page-size="pageSize" :current="pageCurrent" :select-item="selectItem" current-key="userId"
      @on-select="onSelect" @on-current="onCurrent" @on-change="changePage" @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange">
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'userName'">
            <a
              v-if="link"
              href="javascript:;"
              @click.stop="linkEvent('affairsConsultExamination', scope.row, {
                userId: scope.row.userId,
                schedulingCode:classExist?changeData.schedulingCode:changeData[scope.$index].schedulingCode,
                classCode:classExist?changeData.classCode:scope.row.userId,
                evaluationCode: scope.row.evaluationCode,
                practiceEvaluationCode: scope.row.practiceEvaluationCode,
                userName: scope.row.userName,
                sectionTime,
                sectionSeason,
                content: scope.row.content,
                curriculumCode:classExist?changeData.curriculumCode:changeData[scope.$index].curriculumCode,
                answerTime: $route.query.answerTime,
                resultList:$route.query.resultList
            })">
              {{ scope.row.userName || "-" }}
            </a>
          </span>
          <span v-else-if="item == 'courseState'">
            <span v-if="scope.row.courseState === 0">未出勤</span>
            <span v-else>已出勤</span>
          </span>
          <span v-else-if="item == 'reviewState'">
            <span v-if="scope.row.reviewState === 0" style="color:#FFA126;">未批阅</span>
            <span v-else>已批阅</span>
          </span>
          <span v-else-if="item == 'score'">
            <span>{{ scope.row.score ? scope.row.score : 0 }}</span>
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config'
import theoryConfig from '../theoryDetails/config'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { affairDetailsApi, studentAffairDetailsApi } from '@/api/teachingAffairs/index.js'
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: theoryConfig.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'likeName', label: '学生姓名', master: true },
        { key: 'courseState', label: '出勤状态', type: 'radio', valueList: module.courseState },
        { key: 'studyState', label: '批阅状态', type: 'radio', valueList: module.studyState }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: module.columns,
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'userName',
        'reviewState',
        'courseState',
        'score'
      ],
      courseState: '',
      studyState: '',
      likeName: '',
      classCode: this.$route.query.classCode,
      schedulingCode: this.$route.query.schedulingCode,
      sectionTime: this.$route.query.sectionTime,
      sectionSeason: this.$route.query.sectionSeason,
      curriculumCode: this.$route.query.curriculumCode,
      userNum: this.$route.query.userNum,
      changeData: JSON.parse(this.$route.query.resultList)[0],
      classExist: true
    }
  },
  created() {
    if (JSON.parse(this.$route.query.resultList)[0].className === null) {
      this.changeData = JSON.parse(this.$route.query.resultList)
      this.classExist = false
    }
  },

  methods: {
    getList(changeData) {
      if (changeData) { this.changeData = changeData }
      const resultList = this.changeData instanceof Array ? [...this.changeData] : [this.changeData]
      this.tableLoading = true
      const params = this.getPostData('page', 'limit')
      const data = {
        state: 3,
        courseState: this.courseState,
        studyState: this.studyState,
        likeName: this.likeName,
        classCode: this.classCode,
        schedulingCode: this.schedulingCode,
        resultList: resultList,
        ...params
      }
      this.$emit('transmitTime', data)
      if (this.userNum > 0) {
        studentAffairDetailsApi(data).then(res => {
          if (res.code === 0) {
            this.tableData = res.data.records
            this.tableTotal = res.data.total
          }
          this.tableLoading = false
        }).catch(() => {
          this.tableLoading = false
        })
      } else {
        affairDetailsApi(data).then(res => {
          if (res.code === 0) {
            this.tableData = res.data.records
            this.tableTotal = res.data.total
          }
          this.tableLoading = false
        }).catch(() => {
          this.tableLoading = false
        })
      }
    }
  }
}
</script>

