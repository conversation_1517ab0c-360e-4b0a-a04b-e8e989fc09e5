<template>
  <div class="category-wrap">
    <transverse-list
      :data="examStatusArr"
      :allow-deletion="false"
      :allow-add="false"
      :all="true"
      v-bind="categoryProps"
      title="参考状态"
      @node-click="handleNodeClick($event, 'examStatus')"
    />
  </div>
</template>
<script>
import transverseList from '@/packages/transverse-list/index.vue'
import module from '../config.js'

export default {
  components: {
    transverseList
  },
  props: {
    examStatus: [String, Number]
  },
  data() {
    return {
      moduleName: module.name,
      examStatusArr: module.examStatus,
      categoryProps: {
        label: 'label',
        idName: 'value'
      },
      categoryQuery: {
        examStatus: this.examStatus
      }
    }
  },
  mounted() {
  },
  methods: {
    handleNodeClick(item, key) {
      this.categoryQuery[key] = item.value
      this.$emit('category-query', this.categoryQuery)
    }
  }
}
</script>
