<template>
  <div class="content-wrap-layout">
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      :multiple-page="false"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
    />
  </div>
</template>

<script>
import pageTable from './table/index'
export default {
  components: {
    pageTable
  },
  data() {
    return {
      selectItem: [],
      defaultSelectedArr: []
    }
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {}
  }
}
</script>
