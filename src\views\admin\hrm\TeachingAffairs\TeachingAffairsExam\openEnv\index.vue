<template>
  <div class="experimental-content">
    <el-card v-show="isShow" class="content">
      <div class="button">
        <el-button style="margin-bottom: 10px;" type="primary" icon="el-icon-arrow-left" @click="paramsBack">返回</el-button>
      </div>
      <div v-if="synthesisArr.length > 0" style="display: flex;justify-content: flex-end;" class="button">
        <el-button size="mini" type="primary" style="margin-right: 8px;" @click="scoringScoring">正确</el-button>
        <el-button size="mini" type="primary" @click="scoringDanger">错误</el-button>
      </div>
      <!-- 简答题 -->
      <div v-if="shortAnswerArr.length > 0" class="mb-15">
        <div class="question_types">简答题</div>
        <div v-for="(item, index) in shortAnswerArr" :key="`synthesis${index}`" :id="`qt_synthesis${index}`" class="question_short">
          <div class="flex-space-between">
            <div class="comp-question">
              <span>{{ index + 1 }}、&nbsp;</span>
              <span style="max-width: 100%;" v-html="insertNodeInRichText(item.content, ` (${item.questionScore}分)`)"/>
            </div>
          </div>
        </div>
      </div>
      <!-- 组合题 -->
      <div v-if="synthesisArr.length > 0" class="mb-15">
        <!-- <div class="question_types">综合题</div> -->
        <!-- <div class="question_synthesis"> -->
        <div class="name_question"><span>{{ $route.query.questionName }}</span></div>
        <div v-for="(item, index) in synthesisArr" :key="`synthesis${index}`" :id="`qt_synthesis${index}`" class="question_synthesisArr">
          <div v-for="(q, idx) in item.combinationQuestionBOS" :key="idx">
            <div class="flex-space-between ai-start">
              <div class="comp-question flex-1">
                <div>综合题{{ idx + 1 }}.</div>&nbsp;<div style="max-width: 80%;"><div style="overflow: auto;" v-html="insertNodeInRichText(q.questionName, ` (${q.questionScore}分)`)" /></div>
              </div>
              <div style="min-width: 9%; text-align: right;">
                <Score :value="q.questionStemScore"/>
              </div>
            </div>
            <div v-for="(sub, subIndex) in JSON.parse(q.content)" :key="subIndex" class="comp-content-wrap">
              <div class="flex-space-between ai-start">
                <div class="flex-1 flex-row">
                  <el-checkbox @change="boxChange(q,subIndex,q.scoreArr[subIndex],$event)"/>
                  &nbsp;{{ subIndex + 1 }}.&nbsp;
                  <div style="max-width: 80%;"><div style="overflow: auto;" v-html="insertNodeInRichText(sub, ` (${q.scoreArr[subIndex]}分)`)"/></div>
                </div>
                <div style="min-width: 9%; text-align: right;">
                  <Score :value="q.soreListBOS[subIndex]"/>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- </div> -->
      </div>
    </el-card>
    <div v-if="isShow" class="content_disable" @click="showContent">关联题目<i class="el-icon-caret-left"/></div>
    <div v-if="!isShow" class="content_disable1" @click="showContent">关联题目<i class="el-icon-caret-right"/></div>
    <el-card id="experimentalEnv" :style="{'width': envWidth}">
      <Topo v-if="topologyId" :topo-id="topologyId" style="width: 100%;height: 800px;" topo-type="examinationPermissions"/>
    </el-card>
  </div>
</template>

<script>
import Topo from '@/packages/topo/index'
import Score from '@/views/admin/hrm/TeachingAffairs/TeachingAffairsExam/components/score.vue'
import { insertNodeInRichText } from '@/components/QuestionBank/utils.js'

export default {
  name: 'OpenEnv',
  components: {
    Topo,
    Score
  },
  mixins: [],
  data() {
    return {
      isShow: false,
      envWidth: '100%',
      topologyId: '',
      synthesisArr: [],
      checkData: {},
      allSore: '-',
      watchValue: false,
      chooseQuestionData: [],
      shortAnswerArr: [],
      questionType: ''
    }
  },
  watch: {
    // 解决页面渲染不出现分数的问题
    watchValue() {
      this.$forceUpdate()
    }
  },
  mounted() {
    this.topologyId = this.$route.query.topologyId
    this.questionType = this.$route.query.questionType
    if (this.questionType == '10') {
      this.init()
    } else if (this.questionType == '8') {
      this.initShort()
    }
  },
  methods: {
    insertNodeInRichText: insertNodeInRichText,
    initShort() {
      this.shortAnswerArr = [this.$route.query]
    },
    init() {
      this.synthesisArr = [this.$route.query] // 试卷详情
      const scoreData = JSON.parse(this.$route.query.combinationPoints) // 题目分数
      let soreListBOS = []
      // 老师可以重新打分，优先回显老师打的分数，combinationUserPoints是之前打的分数
      if (JSON.parse(this.$route.query.combinationUserPoints) || JSON.parse(localStorage.getItem('soreList' + this.$route.query.questionCode))) {
        soreListBOS = JSON.parse(localStorage.getItem('soreList' + this.$route.query.questionCode)) || JSON.parse(this.$route.query.combinationUserPoints)
      } else {
        // 把二位数组置为零，和试卷详情,题目分数同结构,方便处理提交参数
        const scoreUrlData = JSON.parse(this.$route.query.combinationPoints)
        soreListBOS = this.replaceTwoDimensionalArrayWithZero(scoreUrlData)
      }
      this.synthesisArr[0].combinationQuestionBOS.map((questionItem, questionIndex) => {
        questionItem.questionScore = scoreData[questionIndex].map(each => Number(each)).reduce((p, q) => p + q) // 主题干总分
        questionItem.scoreArr = scoreData[questionIndex] // 每个子题干的分数
        questionItem.soreListBOS = soreListBOS[questionIndex] // //老师给每个子题干打的分数
        questionItem.questionStemScore = questionItem.soreListBOS.reduce((total, score) => {
          if (score !== null) {
            total += score
          }
          return total
        }) // 老师打每个子题干的总分
      })
    },
    // 把二位数组置为零，和题目分数同结构
    replaceTwoDimensionalArrayWithZero(array) {
      for (let i = 0; i < array.length; i++) {
        for (let j = 0; j < array[i].length; j++) {
          array[i][j] = null
        }
      }
      return array
    },
    paramsBack() {
      if (this.questionType == '10') {
        const soreList = []
        this.synthesisArr[0].combinationQuestionBOS.map((questionItem, questionIndex) => {
          soreList.push(questionItem.soreListBOS)
        })
        // 把老师打的分数放缓存，方便上一个路由页面取出来当参数
        localStorage.setItem('soreList' + this.synthesisArr[0].questionCode, JSON.stringify(soreList))
      }

      this.$router.push({
        name: 'examReviewTraining',
        query: {
          examId: this.$route.query.examId,
          examStatus: this.$route.query.examStatus,
          examCode: this.$route.query.examCode,
          examName: this.$route.query.examName,
          evaluationCode: this.$route.query.evaluationCode,
          examinationId: this.$route.query.examinationId,
          submitType: '1',
          // ...this.$route.query,
          oneLevelTitle: '考试管理',
          oneLevelName: 'ca-exam',
          twoLevelTitle: this.$route.query.twoLevelTitle,
          twoLevelName: this.$route.query.name,
          name: this.$route.query.name
        }
      })
    },
    // 点击复选框
    boxChange(q, i, s, e) {
      if (e) {
        this.checkData = {
          id: q.id,
          index: i,
          sore: s,
          isCheck: e
        }
        this.chooseQuestionData.push(this.checkData)
      } else {
        this.chooseQuestionData = this.chooseQuestionData.filter((item) => !(item.id === q.id && item.index === i))
      }
    },
    scoringScoring() {
      // 如果是打勾的话
      this.watchValue = !this.watchValue
      this.synthesisArr[0].combinationQuestionBOS.map((questionItem, questionIndex) => {
        this.chooseQuestionData.forEach(chooseItem => {
          if (chooseItem.id == questionItem.id) {
            questionItem.soreListBOS.splice(chooseItem.index, 1, chooseItem.sore)
            questionItem.questionStemScore = questionItem.soreListBOS.reduce((total, score) => {
              if (score !== null) {
                total += score
              }
              return total
            })
          }
        })
      })
    },
    scoringDanger() {
      this.watchValue = !this.watchValue
      this.synthesisArr[0].combinationQuestionBOS.map((questionItem, questionIndex) => {
        // 拿到对应题目数据的索引，回显到页面
        this.chooseQuestionData.forEach(chooseItem => {
          if (chooseItem.id == questionItem.id) {
            questionItem.soreListBOS.splice(chooseItem.index, 1, 0)
            questionItem.questionStemScore = questionItem.soreListBOS.reduce((total, score) => {
              if (score !== null) {
                total += score
              }
              return total
            })
          }
        })
      })
    },
    showContent() {
      this.isShow = !this.isShow
      this.isShow ? this.envWidth = '50%' : this.envWidth = '100%'
    }
  }
}
</script>

<style scoped lang="scss">
.question_short {
  padding: 10px;
  font-size: 14px;
  border: 1px solid #d1d1d1;
  border-radius: 10px;
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
}
.question_synthesis {
  padding: 10px;
  font-size: 14px;
  border: 1px solid #d1d1d1;
  border-radius: 10px;
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
}
.question_synthesisArr {
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 10px;
  font-size: 14px;
  border: 1px solid #d1d1d1;
  border-radius: 10px;
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
}
::v-deep {
  .orchestration-create-warp {
    height: 100%;
  }
}
.name_question {
  font-weight: 700;
  font-size: 15px;
}
.create-wrap-layout {
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 880px;
  overflow: auto;
  padding: 20px;
}
  .question_types {
    font-size: 16px;
    font-weight: 600;
    font-family: Source Han Sans CN;
  }
  .comp-question {
    display: flex;
    max-height: 200px;
    overflow-y: auto;
    font-weight: 600;
    font-size: 14px;
    color: rgb(36, 41, 47);
    margin-bottom: 10px;
    >span {
      word-break: break-all;
    }
  }
  .comp-content-wrap {
    border-radius: 10px;
    border: 1px solid #e5e6eb;
    margin: 5px 0 10px;
    padding: 10px 20px;
    position: relative;
    >div:first-child {
      display: flex;
      max-height: 200px;
      overflow-y: auto;
      margin-bottom: 10px;
      >span {
        word-break: break-all;
      }
    }
  }
  .experimental-content{
    position: relative;
    height: 100%;
    display: flex;
    justify-content: flex-start;
    .content {
        width: 49%;
        height: 100%;
        overflow: auto;
        .title{
            padding: 10px 20px;
        }
        .content_item {
            height: 640px;
            padding: 0 20px;
            overflow-y: scroll;
            ._question_list{
                ._question_info{
                  margin-bottom: 20px;
                    .text_success {
                      color: #32a41c;
                    }
                    .text_danger {
                      color: #ff1f1f;
                    }
                    ._question_title{
                      display: flex;
                      justify-content: space-between;
                    }
                    ._question_content{
                      display: flex;
                      justify-content: space-between;
                    }
                }
            }
        }
    }
    .content_disable {
        position: absolute;
        bottom: 0;
        right: 50%;
        width: 20px;
        height: 110px;
        color: #fff;
        background-color: var(--color-600);
        padding-left: 2px;
        cursor: pointer;
    }
    .content_disable1 {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 20px;
        height: 110px;
        color: #fff;
        background-color: var(--color-600);
        padding-left: 2px;
        cursor: pointer;
    }
    #experimentalEnv {
        height: 100%;
        margin-left: 20px;
    }
}
</style>

