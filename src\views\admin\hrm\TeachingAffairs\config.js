const statusArr = [
  { value: '1', label: '未开始', type: 'info' },
  { value: '2', label: '已结束', type: 'danger' },
  { value: '3', label: '进行中', type: 'success' }
]

export default {
  name: 'teachingAffairs',
  columns: {
    'userName': {
      title: '学生姓名',
      master: true
    },
    'reviewState': {
      title: '批阅状态'
    },
    'courseState': {
      title: '出勤状态'
    },
    'score': {
      title: '得分情况'
    },
    'createTime': {
      title: '提交试卷时间'
    },
    'roleName': {
      title: '所属分组'
    }
  },
  courseState: [
    { value: '0', label: '未出勤' },
    { value: '1', label: '已出勤' }
  ],
  studyState: [
    { value: '0', label: '未批阅' },
    { value: '1', label: '已批阅' }
  ],
  // 课程状态
  statusObj: statusArr.reduce((acc, prev) => {
    acc[prev.value] = prev
    return acc
  }, {})
}
