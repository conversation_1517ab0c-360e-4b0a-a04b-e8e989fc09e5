<template>
  <div class="content-wrap-layout">
    <span class="tree-content">
      <el-tree ref="treeRef" :data="treeData" :props="defaultProps" :expand-on-click-node="false" node-key="id" default-expand-all>
        <span slot-scope="{ node, data }" class="custom-tree-node" @mouseenter="mouseenter(data)" @mouseleave="mouseleave(data)">
          <span v-if="data.type == 0" style="padding-left: 40px;">{{ '教学方案：' + node.label }}</span>
          <span v-if="data.type == 1" style="padding-left: 40px;">{{ '课程体系：' + data.name }}</span>
          <span v-if="data.type == 2">课程：
            <span v-if="data.coursePrivacy == 0 && $store.state.user.userInfo.userId != data.createdBy" style="cursor:not-allowed">{{ data.name }}</span>
            <a v-else :href="getHref(data)" @click.prevent="detailLinkEvent(data)">{{ data.name }}</a>
          </span>
          <span v-show="data.show">
            <el-button v-if="data.type == 0" type="text" size="mini" @click="addSchemeFn(data)">
              创建课程体系
            </el-button>
            <el-button v-if="data.type == 1" type="text" size="mini" @click="addCourseFn(data)">
              添加课程
            </el-button>
            <el-button v-if="data.type == 1" type="text" size="mini" @click="editSchemeFn(data)">
              编辑课程体系
            </el-button>
            <el-button v-if="data.type == 1" type="text" size="mini" @click="deleteSchemeFn(data)">
              删除课程体系
            </el-button>
            <el-button v-if="data.type == 2" type="text" size="mini" @click="deleteCourseFn(data)">
              移除课程
            </el-button>
          </span>
        </span>
      </el-tree>
    </span>
    <!-- 侧拉弹窗 -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      append-to-body
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :data="treeItem"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
    <!-- 弹窗 -->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="treeItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
  </div>
</template>

<script>
import addScheme from './modal-add-scheme.vue'
import addCourse from './modal-add-course.vue'
import editScheme from './modal-edit-scheme.vue'
import deleteScheme from './modal-delete-scheme.vue'
import deleteCourse from './modal-delete-course.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import { lessonPlanDetailQuery } from '@/api/teacher/index.js'
export default {
  components: {
    addScheme,
    addCourse,
    editScheme,
    deleteScheme,
    deleteCourse
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      id: 1000,
      drawerAction: ['addCourse'], // 侧拉弹窗
      // 弹窗title
      titleMapping: {
        'addScheme': '创建课程体系',
        'addCourse': '添加课程',
        'editScheme': '编辑课程体系',
        'deleteScheme': '删除课程体系',
        'deleteCourse': '删除课程'
      },
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      treeData: [],
      treeItem: []
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.query.hasOwnProperty('id') ? to.query.id : null
      const fromId = from.query.hasOwnProperty('id') ? from.query.id : null
      if (toId !== fromId) {
        this.treeData = []
        this.detailQuery()
      }
    }
  },
  mounted() {
    this.detailQuery()
  },
  methods: {
    // 新增体系
    addSchemeFn(data) {
      this.treeItem = [data]
      this.clickDrop('addScheme', this.treeItem)
    },
    // 删除体系
    deleteSchemeFn(data) {
      this.treeItem = [data]
      this.clickDrop('deleteScheme', this.treeItem)
    },
    // 编辑体系
    editSchemeFn(data) {
      this.treeItem = [data]
      this.clickDrop('editScheme', this.treeItem)
    },
    // 新增课程
    addCourseFn(data) {
      this.treeItem = [data]
      this.clickDrop('addCourse', this.treeItem)
    },
    // 删除课程
    deleteCourseFn(data) {
      this.treeItem = [data]
      this.clickDrop('deleteCourse', this.treeItem)
    },
    // 获取方案详情
    detailQuery() {
      const params = {
        lessonPlanId: this.$route.query.id
      }
      lessonPlanDetailQuery(params).then(res => {
        this.treeData.push(res.data)
        this.treeData.forEach(item => {
          item.type = 0
          if (item.children) {
            item.children.forEach(it => {
              it.type = 1
              if (it.children) {
                it.children.forEach(i => {
                  i.type = 2
                })
              }
            })
          }
        })
      })
    },
    mouseenter(data) {
      this.$set(data, 'show', true)
    },
    mouseleave(data) {
      this.$set(data, 'show', false)
    },
    // 侧拉回调
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'confirm_course') {
        this.formData.selectedCourse = data
        this.drawerClose()
      } else if (type === 'refresh') {
        this.treeData = []
        this.detailQuery()
      }
    },
    // 弹窗回调
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.treeData = []
        this.detailQuery()
      }
    },
    // 列表点击
    detailLinkEvent: function(data) {
      this.$router.push({
        name: 'TeacherProgrammeCourseDetail',
        query: Object.assign({}, this.$route.query, {
          courseId: data.id,
          courseName: data.name,
          des: data.courseDescription,
          type: 1
        })
      })
    },
    getHref: function(data) {
      const route = this.$router.resolve({
        name: 'TeacherProgrammeCourseDetail',
        query: Object.assign({}, this.$route.query, {
          courseId: data.id,
          courseName: data.name,
          des: data.courseDescription,
          type: 1
        })
      })
      return route.href
    }
  }
}
</script>

<style lang="scss" scoped>
.content-wrap-layout {
  margin-top: 59px;
  border-top: 1px solid #E4E7ED;
  height: calc(100% - 60px);
}
.tree-content /deep/ {
  width: 99%;
  margin-top: 15px;
  .custom-tree-node {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .el-tree .el-tree-node__expand-icon.expanded {
    width: 0px;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  .el-tree .el-tree-node__expand-icon.el-icon-caret-right {
    width: 0px;
  }
  .el-tree .el-icon-caret-right:before {
    width: 0px;
    content: "\e723";
    font-size: 16px;
    color: #c2c2c2;
    padding-left: 18px;
  }

  .el-tree .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {
    width: 0px;
    content: "\e722";
    font-size: 16px;
    color: #c2c2c2;
  }

  overflow-y:auto;

  .el-tree>.el-tree-node:after {
    border-top: none;
  }

  .el-tree-node {
    position: relative;
    padding-left: 14px;
  }

  .el-tree-node__expand-icon.is-leaf {
    display: none;
  }

  .el-tree-node__children {
    padding-left: 14px;
  }

  .el-tree-node :last-child:before {
    height: 38px;
  }

  .el-tree>.el-tree-node:before {
    border-left: none;
  }

  .el-tree>.el-tree-node:after {
    border-top: none;
  }

  .el-tree-node:before {
    content: '';
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:after {
    content: '';
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:before {
    border-left: 2px solid #c2c2c2;
    bottom: 0px;
    top: -18px;
    width: 1px;
  }

  .el-tree-node:after {
    border-top: 2px solid #c2c2c2;
    height: 20px;
    top: 20px;
    width: 18px;
  }

  .el-tree .el-tree-node__expand-icon.expanded {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  .el-tree-node__content {
    border: 2px solid #c2c2c2;
    margin-bottom: 5px;
    padding: 20px;
  }

  .el-tree-node__content>.el-tree-node__expand-icon {
    padding: 0;
  }
}
</style>
