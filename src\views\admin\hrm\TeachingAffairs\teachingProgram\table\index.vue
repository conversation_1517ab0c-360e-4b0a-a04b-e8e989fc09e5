<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索教案名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      :type="'list'"
      current-key="userId"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'status'">
            <span v-if="scope.row.status == 1">{{ '未完成' }}</span>
            <span v-else-if="scope.row.status == 0">{{ '已完成' }}</span>
          </span>
          <span v-else-if="item == 'planName'">
            <a
              v-if="link"
              href="javascript:;"
              @click.stop="linkEvent('headTeachingPlanDetail', scope.row, {
                id: scope.row.lessonPlanId, name: scope.row.planName, des: scope.row.description,
                parentName:$route.meta.parentName,parentTitle:$route.meta.parentTitle,titleName:$route.meta.name,
                icon:$route.meta.icon
            })">{{ scope.row.planName || "-" }}
            </a>
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { getLessonPlanSchedulingInfo } from '@/api/teachingAffairs/index.js'
import CountPopover from '@/components/CountPopover/index'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol,
    CountPopover
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'planName', label: '教案名称', placeholder: '请输入教案名称', master: true }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'planName': {
          title: '教案名称', master: true
        },
        'status': {
          title: '排课情况'
        },
        'name': {
          title: '班级/学员'
        }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'planName',
        'status',
        'name'
      ],
      userId: null,
      queryParameters: {}
    }
  },
  methods: {
    getList() {
      this.tableLoading = true
      const params = this.getPostData('page', 'limit')
      const data = {
        ...params,
        planName: this.searchData.planName || ''
      }
      getLessonPlanSchedulingInfo(Object.assign(data, this.queryParameters)).then((res) => {
        if (res.code === 0) {
          this.tableData = res.data.records
          this.tableTotal = res.data.total
        }
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
