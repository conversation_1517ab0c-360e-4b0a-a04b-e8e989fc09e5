<template>
  <div v-loading="loading" element-loading-text="正在计算成绩，请稍候..." class="dialog-wrap set-up-stage" style="padding:0;">
    <div class="dialog-wrap-content">
      <el-form v-loading="tableLoading" ref="form" :model="form" :rules="rules" label-position="left" label-width="0px">
        <el-row :gutter="15">
          <el-col :span="11" :offset="2">阶段名称</el-col>
          <el-col :span="11">期望满分</el-col>
        </el-row>
        <el-row v-for="(item, index) in form.sceneInstanceList" :key="index" :gutter="15">
          <el-col :span="2">
            <el-form-item>
              <el-checkbox v-model="item.checked" />
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item>
              <el-input v-model.trim="tableData[index].sceneName" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item>
              <el-input-number
                v-model.trim="item.scoreSystemTeam"
                :disabled="!item.checked"
                :step="1" :min="0"
                :controls="false"
                type="number"
                style="width: 85%"
              />
              <el-tooltip transfer>
                <i class="el-icon-question mr-5" style="font-size: 16px;" />
                <div slot="content" class="score-tooltip">
                  <div>红方满分：{{ item.redTeamMaxScore || 0 }}</div>
                  <div>蓝方满分：{{ item.blueTeamMaxScore || 0 }}</div>
                </div>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="dialog-footer" style="margin: 0;">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!form.sceneInstanceList.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { queryMatchSceneInstanceVOPage, calculateScore, setCalculateRule } from '@/api/match/index.js'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      tableLoading: true,
      loading: false,
      viewKey: '',
      postKey: '',
      id: this.$route.params.id || '',
      tableData: [],
      form: {
        sceneInstanceList: []
      },
      rules: {
        'proportionTeam': [
          { type: 'number', min: 0, max: 100, message: '输入范围：0-100', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    proportionTotal() {
      let num = 0
      this.form.sceneInstanceList.forEach(item => {
        if (item.checked && item.proportionTeam) {
          num = num + item.proportionTeam
        }
      })
      return num
    }
  },
  created() {
    this.getData()
  },
  methods: {
    close() {
      this.$emit('close')
    },
    getData() {
      const params = {
        id: this.id,
        pageType: 0
      }
      queryMatchSceneInstanceVOPage(params).then(res => {
        this.tableLoading = false
        this.tableData = res.data.records
        this.tableData.forEach(item => {
          this.form.sceneInstanceList.push({
            'checked': item.participateComputeTeam == 1,
            'redTeamMaxScore': item.redTeamMaxScore,
            'blueTeamMaxScore': item.blueTeamMaxScore,
            'sceneInstanceId': item.sourceSceneId,
            'scoreSystemTeam': (item.scoreSystemTeam || item.scoreSystemTeam == 0) ? item.scoreSystemTeam : undefined,
            'proportionTeam': item.proportionTeam
          })
        })
      }).catch(() => {
        this.tableLoading = false
      })
    },
    confirm: function() {
      this.loading = true
      const sceneInstanceList = this.form.sceneInstanceList.filter(item => item.checked).map(item => {
        return {
          'sceneInstanceId': item.sceneInstanceId,
          'scoreSystemTeam': (item.scoreSystemTeam || item.scoreSystemTeam == 0) ? item.scoreSystemTeam : null,
          'proportionTeam': item.proportionTeam
        }
      })
      const params = {
        matchId: this.id,
        sceneInstanceList: sceneInstanceList,
        type: 2,
        model: 1
      }
      setCalculateRule(params).then(r => {
        calculateScore(params).then(res => {
          this.$emit('call', 'refresh')
          this.loading = false
          this.close()
        }).catch(() => {
          this.loading = false
        })
      })
    }
  }
}
</script>
<style lang="less" scoped>
  .set-up-stage {
    ::v-deep {
      .el-checkbox__input.is-checked + .el-checkbox__label {
        color: #333;
      }
      .is-disabled {
        .el-checkbox__input.is-checked + .el-checkbox__label {
          color: #C0C4CC !important;
        }
      }
    }
  }
  .dialog-wrap-content {
    max-height: 400px;
    overflow: auto;
    padding: 20px;
  }
</style>
