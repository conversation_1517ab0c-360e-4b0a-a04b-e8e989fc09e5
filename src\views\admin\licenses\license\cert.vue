<template>
  <div class="content-wrap-layout">
    <top-nav />
    <div class="cert-box">
      <div v-if="licensesInfo" ref="createCert" class="creaet-cert">
        <img class="cert-img" src="@/assets/images/cert.png">
        <div class="cert-title">许可证书</div>
        <div class="cert-platform-name">{{ licensesInfo.productName || productName }}</div>
        <div class="cert-company-name">{{ licensesInfo.customerName || '-' }}</div>
        <div class="cert-before-time">生效时间: {{ licensesInfo['issuedDate'] ? formatTime(licensesInfo['issuedDate']) : '-' }}</div>
        <div class="cert-after-time">截止时间: {{ licensesInfo['expiredDate'] ? formatTime(licensesInfo['expiredDate']) : '-' }}</div>
        <canvas id="cert-chapter" width="200" height="200" />
        <img class="accept-img" src="@/assets/images/accept.png">
        <img id="logo-fav" src="@/assets/images/logo_fav.png">
        <span class="cert-type">{{ licenseType[licensesInfo.type] }}</span>
      </div>
      <div v-loading="loading" v-else class="h-100"/>
      <div ref="showCert" class="show-cert">
        <el-image :src="imgUrl" fit="fill" />
      </div>
    </div>
  </div>
</template>

<script>
import topNav from './index_top_nav'
import license from '../mixins/license'
import html2canvas from 'html2canvas'
export default {
  name: 'Cert',
  components: {
    topNav
  },
  mixins: [license],
  data() {
    return {
      loading: true,
      imgUrl: ''
    }
  },
  created() {
    Promise.all([this.getData()]).then((data) => {
      this.$nextTick(() => {
        this.getCert()
      })
    })
  },
  methods: {
    // 生成证书
    getCert() {
      const createCert = this.$refs['createCert'] // 需要截图的包裹的（原生的）DOM 对象
      const showCert = this.$refs['showCert']
      const width = createCert.offsetWidth // 获取dom 宽度
      const height = createCert.offsetHeight // 获取dom 高度
      const canvas = document.createElement('canvas') // 创建一个canvas节点
      const scale = 1 // 定义任意放大倍数 支持小数
      canvas.width = width * scale // 定义canvas 宽度 * 缩放
      canvas.height = height * scale // 定义canvas高度 *缩放
      canvas.getContext('2d').scale(scale, scale) // 获取context,设置scale
      const opts = {
        scale: scale, // 添加的scale 参数
        canvas: canvas, // 自定义 canvas
        // logging: true, //日志开关，便于查看html2canvas的内部执行流程
        width: width, // dom 原始宽度
        height: height,
        useCORS: true, // 【重要】开启跨域配置
        backgroundColor: null
      }
      html2canvas(createCert, opts).then(canvas => {
        var context = canvas.getContext('2d')

        // 关闭抗锯齿
        context.mozImageSmoothingEnabled = false
        context.webkitImageSmoothingEnabled = false
        context.msImageSmoothingEnabled = false
        context.imageSmoothingEnabled = false
        // 删除html元素
        createCert.parentNode.removeChild(createCert)
        const imgUrl = canvas.toDataURL('image/png')
        this.imgUrl = imgUrl
        showCert.style.visibility = 'visible'
        this.$nextTick(() => {
          this.loading = false
        })
      })
    }
  }
}
</script>
<style lang="less" scoped>
.cert-box {
  position: relative;
  flex: 1;
  overflow: hidden;
  .creaet-cert {
    top: 9999px;
    position: relative;
    width: 1200px;
    height: 700px;
    >div {
      position: absolute;
      width: 100%;
      text-align: center;
      font-weight: 500;
      letter-spacing: 5px;
    }
    .cert-img {
      width: 100%;
      height: 100%;
    }
    .cert-title {
      font-size: 50px;
      top: 153px;
    }
    .cert-platform-name {
      top: 325px;
      font-size: 30px;
    }
    .cert-company-name {
      top: 395px;
      font-size: 20px;
    }
    .cert-before-time, .cert-after-time {
      width: 50%;
      font-size: 13px;
      letter-spacing: 1px;
      top: 500px;
    }
    .cert-before-time {
      left: 70px;
    }
    .cert-after-time {
      right: 70px;
    }
    #cert-chapter {
      position: absolute;
      right: 50px;
      bottom: 30px;
      transform: scale(0.7);
      transform-origin: 0 0;
    }
    .accept-img, .refuse-img {
      position: absolute;
      width: 100px;
      top: 65px;
      left: 65px;
    }
    #logo-fav {
      position: absolute;
      left: 570px;
      bottom: 170px;
    }
    .cert-type {
      color: rgba(0, 113, 48, 1);
      position: absolute;
      top: 94px;
      left: 85px;
      font-size: 16px;
      font-weight: bold;
      letter-spacing: 5px;
      transform: rotate(-10deg);
    }
  }
  .show-cert {
    visibility: hidden;
    width: 100%;
    height: 100%;
    padding: 15px;
    background: #fff;
    overflow-x: auto;
    overflow-y: hidden;
    .el-image {
      min-width: 1200px;
      max-width: 1300px;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
