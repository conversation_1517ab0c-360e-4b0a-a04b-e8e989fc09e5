<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <!-- <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        /> -->
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索人员名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      type="list"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'level'">{{ targetConf.levelArr[scope.row.level - 1] ? targetConf.levelArr[scope.row.level - 1].label : '-' }}</span>
          <span v-else-if="item === 'isPass'">
            {{ scope.row[item] === 1 ? '已通过' : '未通过' }}
          </span>
          <span v-else-if="item === 'score'">
            {{ scope.row[item] || 0 }}
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import targetConf from '../../../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { holeTargetUserRefPage } from '@/api/loophole/target'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      targetConf: targetConf,
      // 搜索配置项
      searchKeyList: [
        { key: 'userName', label: '人员名称', master: true },
        { key: 'holeName', label: '漏洞名称' },
        {
          key: 'level',
          label: '难度',
          type: 'radio',
          valueList: targetConf.levelArr
        },
        { key: 'holeType', label: '漏洞类型' },
        { key: 'time_range', type: 'time_range', label: '提交时间' }
      ],
      camp: '',
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'userName': { title: '人员名称', master: true },
        'holeName': { title: '漏洞名称' },
        'level': { title: '难度' },
        'holeType': { title: '漏洞类型' },
        'isPass': { title: '挑战状态' },
        'score': { title: '成绩' },
        'passTime': { title: '提交时间' }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'userName',
        'holeName',
        'level',
        'holeType',
        'isPass',
        'score',
        'passTime'
      ]
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params.passTimeBegin = params.startTime
      params.passTimeEnd = params.endTime
      params.holeTargetId = this.$route.params.id
      delete params.startTime
      delete params.endTime
      holeTargetUserRefPage(params).then((res) => {
        if (res.code === 0 || res.code === 200) {
          this.tableData = res.data.records
          this.tableTotal = Number(res.data.total)
          this.tableLoading = false
        }
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
