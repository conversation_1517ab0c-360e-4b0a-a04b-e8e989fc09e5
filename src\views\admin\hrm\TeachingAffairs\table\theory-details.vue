<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length" :data="columnsObj" :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol" />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView" :search-key-list="searchKeyListView" default-placeholder="默认搜索学生姓名"
      @search="searchMultiple" />
    <!-- 列表 -->
    <t-table-view
      ref="tableView" :height="height" :single="single" :loading="tableLoading" :data="tableData"
      :total="tableTotal" :page-size="pageSize" :current="pageCurrent" :select-item="selectItem" current-key="userId"
      @on-select="onSelect" @on-current="onCurrent" @on-change="changePage" @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange">
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'userName'">
            <a
              v-if="link"
              :href="getHref(scope.row)"
              @click.prevent="linkEvent('consultTheory', scope.row, {
                userId: scope.row.userId,
                schedulingCode: classExist ? changeData.schedulingCode : getUserData(scope.row).schedulingCode,
                schedulingType: $route.query.schedulingType,
                isExamMode: $route.query.isExamMode,
                classCode:classExist?changeData.classCode:scope.row.userId,
                evaluationCode: scope.row.evaluationCode,
                practiceEvaluationCode: scope.row.practiceEvaluationCode,
                userName: scope.row.userName,
                sectionTime,
                sectionSeason,
                content: scope.row.content,
                curriculumCode: classExist ? changeData.curriculumCode: getUserData(scope.row).curriculumCode,
                curriculumType: $route.query.curriculumType,
                classStatus: $route.query.classStatus,
                answerTime: $route.query.answerTime,
                isDynamic: scope.row.isDynamic // 是否是动态 1是动态 0不是动态
            })">
              {{ scope.row.userName || "-" }}
            </a>
          </span>
          <span v-else-if="item == 'courseState'">
            <span v-if="scope.row.courseState === 0">未出勤</span>
            <span v-else>已出勤</span>
          </span>
          <span v-else-if="item == 'reviewState'">
            <span v-if="scope.row.reviewState === 0" style="color:#FFA126;">未批阅</span>
            <span v-else>已批阅</span>
          </span>
          <span v-else-if="item == 'score'">
            <span>{{ scope.row.score ? scope.row.score : 0 }}</span>
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config'
import theoryConfig from '../theoryDetails/config'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { affairDetailsApi, studentAffairDetailsApi } from '@/api/teachingAffairs/index.js'
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: theoryConfig.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'likeName', label: '学生姓名', master: true },
        { key: 'courseState', label: '出勤状态', type: 'radio', valueList: module.courseState },
        { key: 'reviewState', label: '批阅状态', type: 'radio', valueList: module.studyState }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'userName': {
          title: '学生姓名',
          master: true
        },
        'reviewState': {
          title: '批阅状态'
        },
        'courseState': {
          title: '出勤状态'
        },
        'score': {
          title: '得分情况'
        },
        'createTime': {
          title: '提交试卷时间'
        }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'userName',
        'reviewState',
        'courseState',
        'score',
        'createTime'
      ],
      courseState: '',
      reviewState: '',
      likeName: '',
      classCode: this.$route.query.classCode,
      schedulingCode: this.$route.query.schedulingCode,
      sectionTime: this.$route.query.sectionTime,
      sectionSeason: this.$route.query.sectionSeason,
      userNum: this.$route.query.userNum,
      resultList: [],
      changeData: {},
      classExist: true
    }
  },
  watch: {
    '$route': function() {
      this.classCode = this.$route.query.classCode
      this.schedulingCode = this.$route.query.schedulingCode
      this.sectionTime = this.$route.query.sectionTime
      this.sectionSeason = this.$route.query.sectionSeason
      this.userNum = this.$route.query.userNum
      this.getList()
    }
  },
  methods: {
    getList(changeData) {
      const _resultList = JSON.parse(localStorage.getItem(`manageAffairs_resultList`) || '[]')
      this.resultList = _resultList
      this.changeData = _resultList[0]
      // 是学员
      if (this.resultList[0].className === null) {
        this.changeData = this.resultList
        this.classExist = false
      }
      if (changeData) { this.changeData = changeData }
      const resultList = this.changeData instanceof Array ? [...this.resultList] : [this.changeData]
      this.tableLoading = true
      const params = this.getPostData('page', 'limit')
      const data = {
        state: 1,
        courseState: this.courseState,
        reviewState: this.reviewState,
        likeName: this.likeName,
        classCode: this.classCode,
        schedulingCode: this.schedulingCode,
        resultList: resultList,
        ...params
      }
      this.$emit('transmitTime', data)
      if (this.userNum > 0) {
        studentAffairDetailsApi(data).then(res => {
          if (res.code === 0) {
            this.tableData = res.data.records
            this.tableTotal = res.data.total
          }
          this.tableLoading = false
        }).catch(() => {
          this.tableLoading = false
        })
      } else {
        affairDetailsApi(data).then(res => {
          if (res.code === 0) {
            this.tableData = res.data.records
            this.tableTotal = res.data.total
          }
          this.tableLoading = false
        }).catch(() => {
          this.tableLoading = false
        })
      }
    },
    // 获取对应学员的排课数据
    getUserData(row) {
      const data = this.changeData.find(item => item.classCode == row.userId)
      return data
    },
    getHref(row) {
      const route = this.$router.resolve({
        name: 'consultTheory',
        query: {
          userId: row.userId,
          schedulingCode: this.classExist ? this.changeData.schedulingCode : this.getUserData(row).schedulingCode,
          schedulingType: this.$route.query.schedulingType,
          isExamMode: this.$route.query.isExamMode,
          classCode: this.classExist ? this.changeData.classCode : row.userId,
          evaluationCode: row.evaluationCode,
          practiceEvaluationCode: row.practiceEvaluationCode,
          userName: row.userName,
          sectionTime: this.sectionTime,
          sectionSeason: this.sectionSeason,
          content: row.content,
          curriculumCode: this.classExist ? this.changeData.curriculumCode : this.getUserData(row).curriculumCode,
          curriculumType: this.$route.query.curriculumType,
          classStatus: this.$route.query.classStatus,
          answerTime: this.$route.query.answerTime,
          isDynamic: row.isDynamic // 是否是动态 1是动态 0不是动态
        }
      })
      return route.href
    }
  }
}
</script>

