<template>
  <div v-loading="loading" class="dialog-wrap">
    <div class="dialog-wrap-content">
      <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="80px" @submit.native.prevent>
        <el-form-item label="名称:" prop="name">
          <el-input
            v-model.trim="formData.name"
            maxlength="64"
            show-word-limit
            placeholder="请输入内容,1-64个字符以内"
          />
        </el-form-item>
      </el-form>
    </div>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import validate from '@/packages/validate'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import { lessonPlanHierarchySave } from '@/api/teacher/index.js'
export default {
  components: {
  },
  mixins: [mixinsActionMenu],
  props: {
    // 传入数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      validate: validate,
      formData: {
        name: ''
      },
      rules: {
        name: [validate.required(), validate.base_name]
      },
      id: 1
    }
  },
  created() {
  },
  methods: {
    // 新增节点
    addNode() {
      const newChild = { id: this.id++, name: this.formData.name, children: [], type: 1 }
      if (!this.data[0].children) {
        this.$set(this.data[0], 'children', [])
      }
      this.data[0].children.unshift(newChild)
    },
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const params = {
            hierarchyName: this.formData.name,
            lessonPlanId: this.$route.query.id
          }
          lessonPlanHierarchySave(params).then((res) => {
            if (res.code === 0) {
              this.$message({
                message: '创建成功',
                type: 'success'
              })
              this.addNode()
              this.$emit('call', 'refresh')
              this.close()
            }
          }).catch(() => {
            this.loading = false
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>


