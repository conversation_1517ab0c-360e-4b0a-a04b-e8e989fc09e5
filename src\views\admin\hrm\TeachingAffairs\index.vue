<template>
  <div class="content-wrap-layout">
    <contentHeader/>
    <category :category-name="moduleName" :class-status="classStatus" @category-query="categoryQuery" />
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      :filter-data="{ 'classStatus': classStatus }"
      :cache-pattern="true"
      default-selected-key="schedulingCode"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
      @transmitTime="transmitTime"
    >
      <action-menu
        slot="action"
        :module-name="moduleName"
        :select-item="selectItem"
        :time-params="timeParams"
        @call="actionHandler"
      />
    </page-table>
    <el-drawer
      :visible.sync="detailShow"
      :modal="false"
      size="75%"
      append-to-body
      class="detail-view"
      @close="closeDetail"
    >
      <!-- 项目详情-小组列表 -->
      <project-detail v-if="detailShow && ($route.name === 'listGroups')" />
      <!-- 项目详情-学生列表 -->
      <student-detail v-if="detailShow && ($route.name === 'studentList')" />
      <!-- 理论课程详情 -->
      <theory-detail v-if="detailShow && ($route.name === 'affairsTheory')" />
      <!-- 仿真课程详情 -->
      <simulation-detail v-if="detailShow && ($route.name === 'affairsSimulation')" />
    </el-drawer>
  </div>
</template>

<script>
import moduleConf from './config'
import category from './category/index.vue'
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
import contentHeader from './content-header.vue'
import lodash from 'lodash'
import projectDetail from '@/views/admin/hrm/educationalTasks/list-groups/index'
import studentDetail from '@/views/admin/hrm/educationalTasks/student-list/index'
import theoryDetail from '@/views/admin/hrm/TeachingAffairs/theoryDetails/index'
import simulationDetail from '@/views/admin/hrm/TeachingAffairs/simulationDetails/index'
import moduleMixin from '@/packages/mixins/module_list'

export default {
  name: moduleConf.name,
  components: {
    category,
    pageTable,
    actionMenu,
    contentHeader,
    projectDetail,
    studentDetail,
    theoryDetail,
    simulationDetail
  },
  mixins: [moduleMixin],
  data() {
    return {
      detailShowOfName: ['listGroups', 'studentList', 'affairsTheory', 'affairsSimulation'],
      listRouterName: 'teacherAffairs',
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      timeParams: {},
      num: 0,
      classStatus: ''
    }
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      let str = name
      // curriculumType 理论1仿真2
      if (row.curriculumType == '1' && row.schedulingType == '课程') {
        str = 'affairsTheory'
      } else if (row.curriculumType == '2' && row.schedulingType == '课程') {
        str = 'affairsSimulation'
      } else if (row.schedulingType == '考试') {
        str = 'affairsExamination'
      } else {
        str = 'affairsSimulation'
      }
      this.$router.push({ name: str, query: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    transmitTime(val) {
      this.timeParams = val
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {},
    categoryQuery: lodash.debounce(function(obj) {
      this.classStatus = obj.examType
      if (!this.$store.state.cache[this.moduleName]) {
        const obj = {
          data: { searchShow: true },
          key: this.moduleName
        }
        this.$store.commit('SET_CACHE', obj)
      }
      this.$nextTick(() => {
        if (this.num != 0) {
          this.$refs['table']['pageCurrent'] = 1
        } else {
          this.num = this.num + 1
        }
        this.$refs['table'].getList(false)
      })
    }, 500)
  }
}
</script>
