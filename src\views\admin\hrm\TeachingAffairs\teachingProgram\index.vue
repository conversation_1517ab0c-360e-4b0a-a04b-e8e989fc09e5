<template>
  <div class="content-wrap-layout">
    <contentHeader :title="title" :des="des"/>
    <category :status="status" @category-query="categoryQuery" />
    <page-table
      ref="table"
      :filter-data="{ status }"
      :default-selected-arr="defaultSelectedArr"
      :cache-pattern="true"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
    >
      <action-menu
        slot="action"
        :module-name="moduleName"
        :select-item="selectItem"
        @call="actionHandler"
      />
    </page-table>
  </div>
</template>

<script>
import moduleConf from './config'
import category from './category/index'
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
import contentHeader from '../content-header.vue'

export default {
  name: moduleConf.name,
  components: {
    contentHeader,
    pageTable,
    actionMenu,
    category
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      title: '教学事务',
      des: '对教学事务一应数据展示分析模块',
      status: 2
    }
  },
  methods: {
    categoryQuery: function(value) {
      !value && value != 0 ? this.status = 2 : this.status = value
      this.$nextTick(() => {
        this.$refs['table'].getList()
      })
    },
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, query: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {}
  }
}
</script>
