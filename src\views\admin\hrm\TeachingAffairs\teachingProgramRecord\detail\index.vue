<template>
  <div class="content-wrap-layout">
    <span class="tree-content">
      <el-tree ref="treeRef" :data="treeData" :props="defaultProps" :expand-on-click-node="false" node-key="id" default-expand-all>
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <span v-if="data.type == 0">{{ '教学方案：' + node.label }}</span>
          <span v-if="data.type == 1">{{ '课程体系：' + data.name }}</span>
          <span v-if="data.type == 2">课程：<a
            style="color: var(--color-600);"
            href="javascript:;"
            @click.stop="detailLinkEvent(data)">{{ data.name }}</a></span>
        </span>
      </el-tree>
    </span>
  </div>
</template>

<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import { lessonPlanDetailQuery } from '@/api/teachingAffairs/index.js'
export default {
  mixins: [mixinsActionMenu],
  data() {
    return {
      id: 1000,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      treeData: [],
      treeItem: [],
      createdBy: JSON.parse(localStorage.getItem('loginUserInfo')).userId
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.query.hasOwnProperty('id') ? to.query.id : null
      const fromId = from.query.hasOwnProperty('id') ? from.query.id : null
      if (toId !== fromId) {
        this.treeData = []
        this.detailQuery()
      }
    }
  },
  mounted() {
    this.detailQuery()
  },
  methods: {
    // 获取方案详情
    detailQuery() {
      const params = {
        lessonPlanId: this.$route.query.planId || this.$route.query.id
      }
      lessonPlanDetailQuery(params).then(res => {
        this.treeData.push(res.data)
        this.treeData.forEach(item => {
          item.type = 0
          if (item.children) {
            item.children.forEach(it => {
              it.type = 1
              if (it.children) {
                it.children.forEach(i => {
                  i.type = 2
                })
              }
            })
          }
        })
      })
    },
    // 列表点击
    detailLinkEvent: function(data) {
      this.$router.push({
        name: 'courseDetailLibrary',
        query: {
          courseId: data.id,
          name: this.$route.query.name,
          planDescription: this.$route.query.des || this.$route.query.planDescription,
          planName: this.$route.query.planName || this.$route.query.name,
          planId: this.$route.query.planId || this.$route.query.id,
          icon: this.$route.query.icon,
          title: data.name,
          description: data.courseDescription
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content-wrap-layout {
  margin-top: 59px;
  border-top: 1px solid #E4E7ED;
  height: calc(100% - 60px);
}
.tree-content /deep/ {
  width: 99%;
  margin-top: 15px;
  .custom-tree-node {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .el-tree .el-tree-node__expand-icon.expanded {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  .el-tree .el-icon-caret-right:before {
    content: "\e723";
    font-size: 16px;
    color: #c2c2c2;
    padding-left: 18px;
  }

  .el-tree .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {
    content: "\e722";
    font-size: 16px;
    color: #c2c2c2;
  }

  overflow-y:auto;

  .el-tree>.el-tree-node:after {
    border-top: none;
  }

  .el-tree-node {
    position: relative;
    padding-left: 14px;
  }

  .el-tree-node__expand-icon.is-leaf {
    display: none;
  }

  .el-tree-node__children {
    padding-left: 14px;
  }

  .el-tree-node :last-child:before {
    height: 38px;
  }

  .el-tree>.el-tree-node:before {
    border-left: none;
  }

  .el-tree>.el-tree-node:after {
    border-top: none;
  }

  .el-tree-node:before {
    content: '';
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:after {
    content: '';
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:before {
    border-left: 2px solid #c2c2c2;
    bottom: 0px;
    top: -18px;
    width: 1px;
  }

  .el-tree-node:after {
    border-top: 2px solid #c2c2c2;
    height: 20px;
    top: 20px;
    width: 18px;
  }

  .el-tree .el-tree-node__expand-icon.expanded {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  .el-tree-node__content {
    border: 2px solid #c2c2c2;
    margin-bottom: 5px;
    padding: 20px;
  }

  .el-tree-node__content>.el-tree-node__expand-icon {
    padding: 0;
  }
}
</style>
