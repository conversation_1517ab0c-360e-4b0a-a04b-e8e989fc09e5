<template>
  <div v-loading="loading" class="drawer-wrap">
    <course-table
      ref="table"
      :height="null"
      :link="false"
      @on-select="onSelect"
    />
    <div class="drawer-footer">
      <div>
        <el-button :disabled="!selectedItem.length" type="primary" @click="confirm">确定</el-button>
        <el-button type="text" @click="close">取消</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import { lessonPlanHierarchyCourseSave, lessonPlanDetailQuery } from '@/api/teacher/index.js'
import courseTable from './addContentData/index'
export default {
  components: {
    courseTable
  },
  mixins: [mixinsActionMenu],
  props: {
    // 传入数据
    data: {
      type: Array
    }
  },
  data() {
    return {
      loading: false,
      hierarchyId: this.data[0].id,
      id: 1,
      treeData: [],
      addItemId: '',
      selectedItem: [] // 选中的数据
    }
  },
  created() {
    this.detailQuery()
  },
  methods: {
    onSelect(data) {
      this.selectedItem = data
    },
    // 获取方案详情
    detailQuery() {
      const params = {
        lessonPlanId: this.$route.query.id
      }
      lessonPlanDetailQuery(params).then(res => {
        this.treeData = [res.data]
        this.treeData.forEach(item => {
          item.type = 0
          if (item.children) {
            item.children.forEach(it => {
              it.type = 1
              if (it.children) {
                it.children.forEach(i => {
                  i.type = 2
                })
              }
            })
          }
        })
        this.filterAddItem()
      })
    },
    // 查找新增体系节点
    filterAddItem() {
      const addItem = this.treeData[0].children.filter(item => {
        return item.name === this.data[0].name
      })
      this.addItemId = addItem[0].id
    },
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      if (this.selectedItem && this.selectedItem.length > 0) {
        this.loading = true
        const that = this
        const result = this.selectedItem.map(item => {
          const params = {
            hierarchyId: this.addItemId,
            courseId: item.id
          }
          return lessonPlanHierarchyCourseSave(params)
        })
        result.map((item) => {
          item.then((res, error) => {
            that.loading = false
            that.$message.success(`${res.data}`)
            // 实时更新新增节点
            if (!this.data[0].children) {
              this.$set(this.data[0], 'children', [])
            }
            that.$emit('call', 'refresh')
            that.close()
          }).catch((res) => {
            that.$emit('call', 'refresh')
            that.close()
            that.loading = false
          })
        })
      } else {
        this.$message.error('请选择课程')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .drawer-footer{
      display: flex;
      align-items: center;
      height: 8%;
  }
</style>
