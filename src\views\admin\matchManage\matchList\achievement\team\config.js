export default {
  name: 'matchTeamAchievement',
  columnsArr: [
    // 个人赛
    {
      'ranking': {
        title: '排名', master: true
      },
      'realName': {
        title: '姓名'
      },
      'affiliatedUnit': {
        title: '所属单位'
      },
      'score': {
        title: '成绩'
      }
    },
    {
      // 团队赛
      'ranking': {
        title: '排名', master: true
      },
      'teamName': {
        title: '战队名称'
      },
      'affiliatedUnit': {
        title: '所属单位'
      },
      'score': {
        title: '成绩'
      }
    }
  ],
  // 当前显示列key表 默认，如果localStorage有数据将被覆盖
  columnsViewScoreArr: [
    [
      'ranking',
      'realName',
      'affiliatedUnit',
      'score'
    ],
    [
      'ranking',
      'teamName',
      'affiliatedUnit',
      'score'
    ]
  ],
  searchKeyArr: [
    [
      { key: 'realName', label: '姓名', master: true, placeholder: '默认搜索姓名' },
      { key: 'scoreEnd', label: '成绩<=' },
      { key: 'scoreStart', label: '成绩>=' }
    ],
    [
      { key: 'teamName', label: '战队名称', master: true, placeholder: '默认搜索战队名称' },
      { key: 'affiliatedUnit', label: '所属单位' },
      { key: 'scoreEnd', label: '成绩<=' },
      { key: 'scoreStart', label: '成绩>=' }
    ]
  ]
}
