<template>
  <div class="dialog-wrap">
    <Dialog :title="dialogTitle" :visible.sync="setMatchVisible" width="500px" height="240px" @close="close()">
      <template slot="content">
        <div class="title first">
          <i class="el-icon-circle-check" style="color: #6BC33E" />
          <span>比赛创建成功</span>
        </div>
        <div class="title">
          <i class="el-icon-warning-outline" style="color: #F5EA33;" />
          <span>您尚未为比赛添加阶段，是否前往设置？</span>
        </div>
      </template>
      <div slot="footer" class="mt-10" style="text-align: right;">
        <el-button type="text" class="mr-20" @click="close">稍后设置</el-button>
        <el-button type="primary" @click="confirm">前往设置</el-button>
      </div>
    </Dialog>
  </div>
</template>

<script>
import Dialog from '@/components/Dialog'

export default {
  components: {
    Dialog
  },
  props: {
    setMatchVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    bigMatchId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogTitle: '提示'
    }
  },
  methods: {
    close() {
      this.$emit('close')
      this.$router.go(-1)
    },
    confirm: function() {
      this.$router.push({
        name: 'createSchedule',
        query: {
          bigMatchId: this.bigMatchId,
          bigMatchSeasonId: this.id
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.title {
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #333;
  margin-left: 20px;
  span {
    margin-left: 10px;
  }
  i {
    font-size: 20px;
  }
}
.first {
  margin: 30px 0 20px 20px;
}
/deep/ .hy-dialog-title {
  text-align: left;
  margin-left: 20px;
}
</style>
