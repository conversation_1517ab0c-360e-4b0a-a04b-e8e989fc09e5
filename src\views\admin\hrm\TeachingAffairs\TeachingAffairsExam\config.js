export default {
  name: 'NonCertificate',
  examStatus: [
    { value: 'BEGIN', label: '进行中' },
    { value: 'PREPARE', label: '未开始' },
    { value: 'END', label: '已结束' }
  ],
  examLevel: [
    { value: 1, label: '初级' },
    { value: 2, label: '中级' },
    { value: 3, label: '高级' }
  ],
  publicStatus: [
    { value: '1', label: '发布' },
    { value: '0', label: '未发布' }
  ],
  columnsObj: {
    'schedulingResName': {
      title: '考试名称', master: true
    },
    'examStatus': {
      title: '考试状态'
    },
    'distanceTime': {
      title: '时长'
    },
    'beginTime': {
      title: '考试时间'
    },
    'studentName': {
      title: '班级/学员名称'
    },
    'schedulingResType': {
      title: '类型'
    },
    'teacherName': {
      title: '上课教师'
    },
    'assistantTeacherName': {
      title: '上课助教'
    }
  },
  // 当前显示列key表 默认，如果localStorage有数据将被覆盖
  columnsViewArr: [
    'schedulingResName',
    'examStatus',
    'distanceTime',
    'beginTime',
    'studentName',
    'schedulingResType',
    'assistantTeacherName'
  ]
}
