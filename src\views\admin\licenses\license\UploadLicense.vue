<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-width="100px" @submit.native.prevent>
      <el-form-item label="授权证书" prop="file">
        <t-upload
          ref="fileUpload"
          :file-path="uploadPath"
          :file-data="uploadFile"
          :max-size="1024 * 16"
          @change="selectFile"
        >
          <el-button>选择上传文件</el-button>
        </t-upload>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!uploadFile" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import tUpload from '@/packages/upload/index.vue'
import validate from '@/packages/validate'
import { uploadLicense } from '@/api/admin/licenses'
export default {
  name: 'UploadLicense',
  components: { tUpload },
  props: {
    // 传入数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      validate: validate,
      uploadFile: null,
      uploadPath: null,
      formData: {
        file: ''
      },
      rules: {
        'file': [
          validate.required()
        ]
      },
      loading: false
    }
  },
  methods: {
    'selectFile': function(files, path) {
      this.uploadPath = path || null
      this.uploadFile = files.length ? files[0] : null
    },
    // modal点击确定
    'confirm': function(modal) {
      this.loading = true
      const reader = new FileReader()
      reader.readAsText(this.uploadFile, 'UTF-8') // 读取，转换字符编码
      reader.onload = (e) => {
        const postData = { 'content': e.target.result }
        uploadLicense(postData).then((res) => {
          this.loading = false
          this.$message.success(`更新授权许可成功`)
          this.$emit('success')
          this.$emit('close')
          window.location.reload()
        }).catch(() => {
          this.loading = false
        })
      }
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>


