<template>
  <div class="h-100 flex-col">
    <el-breadcrumb style="margin: 15px;" class="detail-breadcrumb" separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="'/manage/training/affairs'">{{ '教学事务' }}</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ name: 'affairsExamination',query:$route.query }">{{ content }}</el-breadcrumb-item>
      <el-breadcrumb-item>{{ '试卷详情' }}</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="flex-1 flex-row h-0">
      <div class="main-left">
        <div class="main-title">
          <div class="blue-spot"/>
          <span>基础教学信息</span>
        </div>
        <div class="main-content">
          <div class="teaching-time">
            <span class="title-name">教学名称:</span>
            <div class="teaching-time_content">{{ content }}</div>
          </div>
          <div class="teaching-time">
            <span class="title-name">教学时间:</span>
            <div class="teaching-time_content">
              <div> <span>{{ sectionTime.split(' ')[0] }}</span>&nbsp;({{ getweekday(sectionTime.split(' ')[0]) }})</div>
              <span>{{ sectionSeason.slice(0, 6) }}{{ sectionSeason.substr(-5) }}</span>
            </div>
          </div>
          <div class="main-title">
            <div class="blue-spot"/>
            <span>{{ affairData.realname }}</span>
          </div>
          <div class="main-content">
            <div class="teaching-time">
              <span class="title-name">得分情况:</span>
              <div class="teaching-time_content">{{ affairData.examScore }}</div>
            </div>
            <!-- <div class="teaching-time">
              <span class="title-name">班级排名:</span>
              <div class="teaching-time_content">{{ affairData.ranking }}</div>
            </div>
            <div class="teaching-time">
              <span class="title-name">班级平均分:</span>
              <div class="teaching-time_content">{{ affairData.avgExamScore }}</div>
            </div> -->
          </div>
          <div class="btn-div">
            <div class="btn2" @click="previousPerson">上一人</div>
            <div class="btn" @click="nextPerson">下一人</div>
          </div>
        </div>
      </div>
      <div class="main p-15 flex-1 h-100">
        <div class="_paper_left h-100 flex-col">
          <div class="_question_details flex-1 h-0">
            <!-- 题目信息 -->
            <div class="_question_list flex-1">
              <!-- 题目类型 -->
              <el-tabs v-model="currentType" @tab-click="handleClick">
                <el-tab-pane
                  v-for="(item, index) in questionTypeList"
                  v-if="item.length > 0"
                  :key="index"
                  :label="item.name"
                  :name="String(item.type)"
                />
              </el-tabs>
              <div
                v-for="(q, index) in questionList"
                :key="q.questionCode"
                :id="`qt_index_${index}`"
                class="_question_info"
              >
                <div class="_question_title_div">
                  <div class="_question_title">{{ index + 1 }}. <span style="display: inline-block;" v-html="q.content"/></div>
                  <div v-if="q.questionAnswer == q.questionUserAnswer" class="questionStudentScore">{{ q.questionStudentScore || 0 }}分</div>
                  <div v-else class="questionStudentScore_red">{{ q.questionStudentScore || 0 }}分</div>
                </div>
                <div v-if="q.questionType == '1'">
                  <div v-for="(op, i) in JSON.parse(q.questionOptions)" :key="op" class="_question_option">
                    <el-radio v-model="singleAnswerList['option' + index]" :label="optionLabel[i]" disabled>{{ optionLabel[i] }}.{{ op }}</el-radio>
                  </div>
                  <div class="information">
                    参考答案：{{ optionLabel[optionLabel.indexOf(q.questionAnswer)] }}
                  </div>
                  <div class="student">
                    学生答案：{{ optionLabel[optionLabel.indexOf(q.questionUserAnswer)] }}
                  </div>
                </div>
                <div v-if="q.questionType == '2'">
                  <el-checkbox-group v-model="multiAnswerList['multiOption' + index]">
                    <div v-for="(op, i) in JSON.parse(q.questionOptions)" :key="op" class="_question_option">
                      <el-checkbox :label="optionLabel[i]" disabled>{{ optionLabel[i] }}. {{ op }}</el-checkbox>
                    </div>
                  </el-checkbox-group>
                  <div class="information">
                    参考答案：<span v-for="(item,index) in (q.questionAnswer||'').split(',')" :key="index">{{ item }}{{ (q.questionAnswer||'').split(',').length-1 === index? '':'、' }}</span>
                  </div>
                  <div v-if="q.questionUserAnswer" class="student">
                    学生答案：<span v-for="(item,index) in (q.questionUserAnswer||'').split(',')" :key="index">{{ item }}{{ (q.questionUserAnswer||'').split(',').length-1 === index? '':'、' }}</span>
                  </div>
                </div>
                <div v-if="q.questionType == '3'">
                  <div class="a-div"><a @click="saveAs(q.fileUrl, q.fileName)">{{ q.fileName }}</a></div>
                  <el-input v-model.trim="q.questionUserAnswer" disabled/>
                  <div class="information">
                    参考答案：{{ q.questionAnswer }}
                  </div>
                  <div class="student">
                    学生答案：{{ q.questionUserAnswer }}
                  </div>
                </div>
                <div v-if="q.questionType == '4'">
                  <div class="a-div"><a @click="saveAs(q.fileUrl, q.fileName)">{{ q.fileName }}</a></div>
                  <el-input v-model.trim="q.questionUserAnswer" disabled/>
                  <div class="information">
                    参考答案：{{ q.questionAnswer }}
                  </div>
                  <div class="student">
                    学生答案：{{ q.questionUserAnswer }}
                  </div>
                </div>
                <div v-if="q.questionType == '5'">
                  <div class="a-div"><a @click="saveAs(q.fileUrl, q.fileName)">{{ q.fileName }}</a></div>
                  <el-input v-model.trim="q.questionUserAnswer" disabled/>
                  <div class="information">
                    参考答案：{{ q.questionAnswer }}
                  </div>
                  <div class="student">
                    学生答案：{{ q.questionUserAnswer }}
                  </div>
                </div>
              </div>
            </div>
            <!-- 题目序号 -->
            <div class="_question_sn">
              <div class="_question_stat">
                <span class="mr-15">总题数:<span class="_question_sn_total">{{ total }}</span>题</span>
                <span>总分数:<span class="_question_sn_total">{{ totalScore }}</span>分</span>
              </div>
              <div class="_question_sn_order">
                <!-- ${ currentOrder == (index + 1) && '_question_sn_order_item_check' }  -->
                <div
                  v-for="(item,index) in questionList"
                  :key="index"
                  :class="`_question_sn_order_item ${item.questionAnswer == item.questionUserAnswer ? 'correct' :'error'}`"
                  @click="handleOrderClick((index+1))"
                >
                  {{ index+1 }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getweekday } from '@/utils'
import { searchExperimentReportApi, queryExamAffairAPI } from '@/api/teacher/index.js'
export default {
  data() {
    return {
      activeName: 'report',
      schedulingCode: this.$route.query.schedulingCode,
      userId: this.$route.query.userId,
      consultList: {},
      docValue: '',
      img: require('@/assets/empty_state.png'),
      evaluationCode: this.$route.query.evaluationCode,
      practiceEvaluationCode: this.$route.query.practiceEvaluationCode,
      classCode: this.$route.query.classCode,
      upAndDown: 0,
      affairData: [],
      currentOrder: 1,
      sectionTime: this.$route.query.sectionTime,
      sectionSeason: this.$route.query.sectionSeason,
      content: this.$route.query.content,
      optionLabel: Array.from({ length: 26 }, (_, index) => String.fromCharCode('A'.charCodeAt(0) + index)),
      questionList: [],
      singleArr: [],
      multiAnswerList: {},
      multiArr: [],
      total: 0, // 总题数
      singleAnswerList: {}, // 单选题答案
      singleOneArr: [], // 单机题数组
      simulatorArr: [], // 仿真题数组
      ctfArr: [], // ctf题数组
      currentType: '1',
      curriculumCode: this.$route.query.curriculumCode,
      questionTypeList: [
        { name: '单选题', type: '1', length: '0' },
        { name: '多选题', type: '2', length: '0' },
        { name: '单机题', type: '3', length: '0' },
        { name: '仿真题', type: '4', length: '0' },
        { name: 'CTF题', type: '5', length: '0' }
      ],
      typeTotalScore: 0,
      totalScore: 0
    }
  },
  created() {
    this.queryPracticeAffair()
  },
  methods: {
    getweekday: getweekday,
    saveAs(blob, filename) {
      if (blob) {
        fetch(blob, {
          method: 'get',
          responseType: 'blob'
        })
          .then((response) => response.blob())
          .then((blob) => {
            const a = document.createElement('a')
            const URL = window.URL || window.webkitURL
            const herf = URL.createObjectURL(blob)
            a.href = herf
            a.download = filename
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(herf)
          })
      }
    },
    // 题目类型 tab 点击
    handleQuestionTypeClick(index) {
      this.currentType = String(index)
      if (this.currentType == '1') {
        this.questionList = this.singleArr
      }
      if (this.currentType == '2') {
        this.questionList = this.multiArr
      }
      if (this.currentType == '3') {
        this.questionList = this.singleOneArr
      }
      if (this.currentType == '4') {
        this.questionList = this.simulatorArr
      }
      if (this.currentType == '5') {
        this.questionList = this.ctfArr
      }
      if (this.questionList.length == 0) {
        this.typeTotalScore = 0
        return
      }
      this.typeTotalScore = this.questionList
        .map((el) => Number(el.questionScore))
        .reduce((p, q) => p + q)
    },
    handleOrderClick(index) {
      this.currentOrder = index
      const el = document.getElementById(`qt_index_${index - 1}`)
      el.scrollIntoView({
        behavior: 'smooth'
      })
    },
    queryPracticeAffair() {
      let userIdList = []
      const resultList = this.$route.query.resultList
      if (resultList) {
        userIdList = JSON.parse(resultList)
      }
      const data = {
        evaluationCode: this.evaluationCode,
        classCode: this.classCode,
        examCode: this.curriculumCode,
        upAndDown: this.upAndDown,
        userId: this.userId,
        userIdList: userIdList,
        schedulingCode: this.schedulingCode
      }
      queryExamAffairAPI(data).then((res) => {
        this.affairData = res.data
        this.userId = res.data.userId
        this.questionList = this.singleArr =
            res.data.examResultsDetailsVos.filter((q) => q.questionType == '1')
        this.multiArr = res.data.examResultsDetailsVos.filter(
          (q) => q.questionType == '2'
        )
        this.singleOneArr = res.data.examResultsDetailsVos.filter(
          (q) => q.questionType == '3'
        )
        this.simulatorArr = res.data.examResultsDetailsVos.filter(
          (q) => q.questionType == '4'
        )
        this.ctfArr = res.data.examResultsDetailsVos.filter(
          (q) => q.questionType == '5'
        )
        if (this.singleArr.length != 0) {
          this.currentType = '1'
        } else if (this.multiArr.length != 0) {
          this.currentType = '2'
        } else if (this.singleOneArr.length != 0) {
          this.currentType = '3'
        } else if (this.simulatorArr.length != 0) {
          this.currentType = '4'
        } else if (this.ctfArr.length != 0) {
          this.currentType = '5'
        }
        this.questionTypeList[0].length = this.singleArr.length
        this.questionTypeList[1].length = this.multiArr.length
        this.questionTypeList[2].length = this.singleOneArr.length
        this.questionTypeList[3].length = this.simulatorArr.length
        this.questionTypeList[4].length = this.ctfArr.length
        this.searchExperimentReport()
        this.handleQuestionTypeClick(this.currentType)
        // 动态添加 v-model
        this.singleArr.forEach((m, i) => {
          this.singleAnswerList['option' + i] = m.questionUserAnswer
        })
        this.multiArr.forEach((m, i) => {
          let anwserList = []
          if (m.questionUserAnswer) {
            anwserList = m.questionUserAnswer.split('')
          }
          this.multiAnswerList['multiOption' + i] = anwserList
        })
        this.total = res.data.examResultsDetailsVos.length
        this.totalScore = res.data.examResultsDetailsVos
          .map((vo) => Number(vo.questionScore))
          .reduce((prev, current) => prev + current)
      })
    },
    searchExperimentReport() {
      const data = {
        schedulingCode: this.schedulingCode,
        userId: this.userId
      }
      searchExperimentReportApi(data).then((res) => {
        this.consultList = res.data || {}
      })
    },
    previousPerson() {
      this.upAndDown = 1
      this.queryPracticeAffair()
    },
    nextPerson() {
      this.upAndDown = 2
      this.queryPracticeAffair()
    },
    goAffairs() {
      this.$router.push({
        path: '/teacher/affairs'
      })
    },
    goPage() {
      this.$router.push({
        path: '/teacher'
      })
    },
    handleClick(tab, event) {
      this.handleQuestionTypeClick(tab.name)
    }
  }
}
</script>
<style lang="scss" scoped>
.h-100 {
  height: 100%;
}
.h-0 {
  height: 0;
  overflow: auto;
}
.main {
  background: #ffffff;
  border-radius: 4px;
}
::v-deep {
  .el-aside {
    padding: 0px 8px 20px 0;
  }
  .el-main {
    padding: 0 0 20px 0;
  }
}
.main-left{
  height: 100%;
  padding: 25px 20px;
  background: #FFFFFF;
  min-width: 248px;
  .main-title{
    display: flex;
    align-items: center;
    .blue-spot{
      width: 4px;
      height: 16px;
      background: #288FEF;
      border-radius: 2px;
      margin-right: 9px;
    }
    span{
      color: #333333;
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
    }
  }
  .main-content{
    padding: 22px 0;
    color: #333333;
    font-size: 14px;
    .teaching-time{
      margin-bottom: 14px;
      display: flex;
      .teaching-time_content{
        width: 123px;
        word-break:break-all;
        line-height: 24px;
      }
    }
    .title-name{
      font-family: Source Han Sans CN;
      line-height: 24px;
      font-weight: 400;
      color: #999999;
      margin-right: 23px;
    }
  }
  .btn-div{
    display: flex;
    align-items: center;
    .btn{
      height: 32px;
      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #FFFFFF;
      padding: 0px 16px;
      background: #288FEF;
      display: flex;
      align-items: center;
      border-radius: 2px;
      cursor: pointer;
    }
    .btn:hover{
      border-radius: 2px;
      cursor: pointer;
      display: flex;
      align-items: center;
      height: 32px;
      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #FFFFFF;
      padding: 0px 16px;
      background: #288FEF;
      opacity: 0.4;
    }
    .btn:active{
      border-radius: 2px;
      cursor: pointer;
      display: flex;
      align-items: center;
      height: 32px;
      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #FFFFFF;
      padding: 0px 16px;
      background: #0790E9;
      opacity: 1;
    }
    .btn2{
      margin: 0 34px 0 14px;
      height: 32px;
      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #288FEF;
      padding: 0px 16px;
      background: #E3F0FC;
      display: flex;
      align-items: center;
      border-radius: 2px;
      cursor: pointer;
      border: 1px solid #288FEF;
    }
    .btn2:hover{
      border-radius: 2px;
      cursor: pointer;
      display: flex;
      align-items: center;
      height: 32px;
      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #288FEF;
      border: 1px solid #288FEF;
      padding: 0px 16px;
      background: #E3F0FC;
      opacity: 0.4;
    }
    .btn2:active{
      border-radius: 2px;
      cursor: pointer;
      display: flex;
      align-items: center;
      height: 32px;
      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #FFFFFF;
      padding: 0px 16px;
      background: #0790E9;
      opacity: 1;
    }
  }
}
._paper_left {
  ._question_header {
    padding: 20px 10px;
    border-radius: 6px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    ._question_type_list {
      display: flex;
      align-items: center;
      color: #999999;

      ._question_type {
        margin-right: 30px;
        cursor: pointer;
      }

      ._question_type_check {
        color: #333333;
        position: relative;

        &:after {
          content: "";
          position: absolute;
          top: 36px;
          left: 50%;
          transform: translateX(-50%);
          background: #288fef;
          width: 42.8px;
          height: 2.1px;
        }
      }
    }
  }

  ._question_details {
    display: flex;
    ._question_list {
      overflow-y: auto;
      .information{
        font-size: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #288FEF;
        line-height: 36px;
      }
      .student{
        font-size: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFA126;
        line-height: 36px;
      }
      ._question_info {
        width: 56vw;
        margin-bottom: 13px;
        padding: 20px;
        background: #FFFFFF;
        ._question_title_div{
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
          ._question_title {
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #333333;
          }
          .a-div{
            margin-bottom: 20px;
            a{
                font-size: 12px;
                color: #288fef;
                text-decoration: underline !important;
            }
        }
          .questionStudentScore_red{
              width: 60px;
              display: flex;
              justify-content: end;
              color: red;
          }
          .questionStudentScore{
              width: 60px;
              display: flex;
              justify-content: end;
              color: greenyellow;
          }
        }
        ._question_option {
          ::v-deep {
            .el-radio__label,.el-checkbox__label{
              font-size: 14px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #333333 !important;
              line-height: 36px;
            }
          }
        }
      }
    }

    ._question_sn {
      width: 280px;
      min-width: 280px;
      background: #ffffff;
      padding: 12px 20px;
      border-left: 1px solid #eee;
      ._question_sn_order {
        margin-top: 50px;
        display: flex;
        flex-wrap: wrap;
        ._question_sn_order_item {
          width: 28px;
          height: 28px;
          color: #fa3026;
          border: 1px solid #fa3026;
          font-size: 14px;
          margin-right: 8px;
          margin-bottom: 8px;
          line-height: 28px;
          text-align: center;
          border-radius: 4px;
          cursor: pointer;
        }
        .correct{
          border: none;
          color: #00B42A;
          background: #E8FFEA;
        }
        .error{
          color: #F53F3F;
          background: #FFECE8;
          border: none;
        }

        ._question_sn_order_item_check {
          background: #288fef;
          color: white;
          border: none;
        }
      }
      ._question_stat {
        font-size: 14px;
        color: #999;
        text-align: right;
        ._question_sn_total {
          color: #288fef;
          margin-left: 5px;
        }
      }
    }
  }
}
/deep/ .el-tabs__nav-wrap::after {
  height: 0;
}
</style>

