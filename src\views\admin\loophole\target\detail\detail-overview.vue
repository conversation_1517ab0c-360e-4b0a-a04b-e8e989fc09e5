<template>
  <el-row :gutter="20">
    <el-col :span="12">
      <detail-card title="基本信息">
        <el-form slot="content" label-position="left" label-width="110px">
          <el-form-item label="名称：">{{ data.name || '-' }}</el-form-item>
          <el-form-item label="漏洞载体：">{{ data.questionName || '-' }}</el-form-item>
          <el-form-item label="漏洞名称：">{{ data.holeName || '-' }}</el-form-item>
          <el-form-item label="漏洞类型：">{{ data.holeType || '-' }}</el-form-item>
          <el-form-item label="漏洞级别：">{{ data.holeLevel || '-' }}</el-form-item>
          <el-form-item label="漏洞简介：">{{ data.holeDesc || '-' }}</el-form-item>
          <el-form-item label="难度：">{{ data.level ? questionConf.levelObjMapping[data.level] : '-' }}</el-form-item>
          <el-form-item label="分数：">{{ data.score || '-' }}</el-form-item>
          <el-form-item label="通关人数：">{{ data.passNum || '-' }}</el-form-item>
          <el-form-item label="挑战人数：">{{ data.tryNum || '-' }}</el-form-item>
          <el-form-item label="创建时间：">{{ data.createTime || '-' }}</el-form-item>
        </el-form>
      </detail-card>
    </el-col>
  </el-row>
</template>

<script>
import questionConf from '../config.js'
import detailCard from '@/packages/detail-view/detail-card.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
export default {
  name: 'DetailOverview',
  components: {
    detailCard
  },
  mixins: [mixinsPageTable],
  props: {
    id: {
      type: [Number, String]
    },
    data: {
      type: Object
    }
  },
  data() {
    return {
      moduleName: 'exam',
      searchKeyList: [],
      // ['A', 'B', 'C', 'D', 'E', ...]
      questionConf: questionConf
    }
  },
  created() {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
.bug-content-title {
  display: flex;
  color: var(--neutral-600);
  font-weight: 500;
  margin-bottom: 12px;
}
</style>
