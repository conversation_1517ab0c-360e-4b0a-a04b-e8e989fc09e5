<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索上课内容"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="schedulingCode"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'content'">
            <el-tooltip placement="top">
              <div slot="content">
                灰色图标表示环境未开启<br>
                绿色图标表示环境已开启<br>
                红色图标表示环境异常<br>
                转圈图标表示环境开启中/关闭中
              </div>
              <span style="position: relative;top: 3px;">
                <img
                  v-if="scope.row.envStatus == 0"
                  src="@/assets/img/exam/server.svg"
                  alt=""
                  width="15">
                <img
                  v-else-if="scope.row.envStatus == 1"
                  src="@/assets/img/exam/server1.svg"
                  alt=""
                  width="15">
                <img
                  v-else-if="scope.row.envStatus == 2"
                  src="@/assets/img/exam/server2.svg"
                  alt=""
                  width="15">
                <img
                  v-else-if="scope.row.envStatus == 3"
                  class="loadding"
                  src="@/assets/img/exam/loadding.svg"
                  alt=""
                  width="15">
              </span>
            </el-tooltip>
            <a
              v-if="link"
              :href="getDetailHref(scope.row)"
              @click.prevent="goToDetail(scope.row)">{{ scope.row.content || "-" }}
            </a>
          </span>
          <span v-else-if="item == 'courseName'">
            <a
              v-if="link && scope.row.schedulingType == '课程'"
              :href="getCourseDetailHref(scope.row)"
              @click.prevent="goToCourseDetails(scope.row)">{{ scope.row.courseName || "-" }}
            </a>
            <span v-else>-</span>
          </span>
          <span v-else-if="item == 'classStatus'">
            <el-badge :type="statusObj[scope.row.classStatus].type" is-dot />{{ statusObj[scope.row.classStatus].label || "-" }}
          </span>
          <span v-else-if="item == 'sectionSeason'">
            <div v-if="scope.row.sectionTime">
              <span class="mr-5">{{ scope.row.sectionTime }}</span>
              <span>{{ scope.row.sectionSeason }}</span>
            </div>
            <div v-else>-</div>
          </span>
          <div v-else-if="item == 'majorName'">
            <!-- 学员集合 -->
            <table-td-multi-col v-if="!scope.row.resultList[0].className" :data="scope.row.userNames" :number="scope.row.userNames.length">
              <div slot="reference">{{ scope.row.userNames[0] }}</div>
              <div v-for="val in scope.row.userNames" :key="val">{{ val }}</div>
            </table-td-multi-col>
            <!-- 班级集合 -->
            <table-td-multi-col v-else :data="scope.row.classNames" :number="scope.row.classNames.length">
              <div slot="reference">{{ scope.row.classNames[0] }}</div>
              <div v-for="val in scope.row.classNames" :key="val">{{ val }}</div>
            </table-td-multi-col>
          </div>
          <span v-else-if="item == 'assistantTeacherName'">
            <!-- 助教集合 -->
            <table-td-multi-col v-if="scope.row.assistantTeacherNames.length" :data="scope.row.assistantTeacherNames" :number="scope.row.assistantTeacherNames.length">
              <div slot="reference">{{ scope.row.assistantTeacherNames[0] }}</div>
              <div v-for="val in scope.row.assistantTeacherNames" :key="val">{{ val }}</div>
            </table-td-multi-col>
          </span>
          <span v-else-if="item == 'schedulingNumber'">
            {{ scope.row.schedulingNumber }} / {{ scope.row.studentNumber || 0 }}
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { manageQueryAffair, envStatusApi } from '@/api/teachingAffairs/index.js'
import CountPopover from '@/components/CountPopover/index'
// 去重
function arrayUnique(arr) {
  const uniqueArray = []
  for (let i = 0; i < arr.length; i += 1) {
    if (uniqueArray.indexOf(arr[i]) === -1) uniqueArray.push(arr[i])
  }
  return uniqueArray
}

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol,
    CountPopover
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'content', label: '上课内容', master: true, placeholder: '请输入上课内容' },
        { key: 'courseName', label: '课程名称', placeholder: '请输入课程名称' },
        { key: 'likeName', label: '班级/学员', placeholder: '请输入班级/学员' },
        { key: 'schedulingType', label: '上课类型', type: 'radio', valueList: [
          { value: '课程', label: '课程' },
          { value: '模拟练习', label: '模拟练习' },
          { value: '项目', label: '项目' }]
        },
        { key: 'time_range', label: '上课时间', type: 'time_range', placeholder: '选择上课时间' }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'content': {
          title: '上课内容', master: true
        },
        'schedulingType': {
          title: '上课类型'
        },
        'courseName': {
          title: '课程名称'
        },
        'majorName': {
          title: '班级/学员'
        },
        'classStatus': {
          title: '上课状态'
        },
        'sectionSeason': {
          title: '上课时间'
        },
        'schedulingNumber': {
          title: '出勤人数'
        },
        'teacherName': {
          title: '上课教师'
        },
        'assistantTeacherName': {
          title: '上课助教'
        }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'content',
        'schedulingType',
        'courseName',
        'majorName',
        'classStatus',
        'sectionSeason',
        'schedulingNumber',
        'teacherName',
        'assistantTeacherName'
      ],
      statusObj: module.statusObj,
      selectTag: 'all',
      timer: null
    }
  },
  beforeDestroy() {
    this.closePollAsyncRequest()
  },
  methods: {
    getList() {
      this.tableLoading = true
      const params = this.getPostData('page', 'limit')
      this.$emit('transmitTime', params)
      manageQueryAffair(params).then(res => {
        if (res.code === 0) {
          this.tableData = res.data.records
          this.tableData.forEach(item => {
            item.userList = Array.from(item.userList)
            // 组装集合数据
            const fields = ['userName', 'className', 'assistantTeacherName']
            fields.forEach(key => {
              item[key + 's'] = arrayUnique(item.resultList.map(v => v[key]))
            })
          })
          this.tableTotal = res.data.total
          this.handleSelection()
          this.pollAsyncRequest()
        }
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      }).finally(() => {
      })
    },
    // 轮询
    pollAsyncRequest(interval = 5000) {
      this.envStatusFn() // 每次轮询之前先立即调用一次，然后接着 5s 后进行轮询更新环境状态
      this.closePollAsyncRequest()
      this.timer = setInterval(() => {
        this.envStatusFn()
      }, interval)
    },
    // 关闭轮询定时器
    closePollAsyncRequest() {
      this.timer && clearInterval(this.timer)
    },
    envStatusFn() {
      this.tableData.forEach(async(item) => {
        const params = {
          courseCode: item.courseCode,
          schedulingCode: item.schedulingCode,
          curriculumCode: item.curriculumCode
        }
        if (item.topologyFlag === '1') {
          const result = await envStatusApi(params)
          if (result.code === 0) {
            const { total, running, error } = result.data
            if (total === 0) { // 未开启
              this.$set(item, 'envStatus', 0)
            } else {
              if (total === running) { // 启动成功
                this.$set(item, 'envStatus', 1)
              } else {
                if (error !== 0) { // 启动失败
                  this.$set(item, 'envStatus', 2)
                } else { // 启动中
                  this.$set(item, 'envStatus', 3)
                }
              }
            }
          }
        }
      })
    },
    // 前往课程详情页面
    goToCourseDetails(item) {
      this.$router.push({
        name: 'affairsCourseDetail',
        query: {
          courseId: item.courseCode,
          courseName: item.courseName,
          enterType: 'course'
        }
      })
    },
    getCourseDetailHref(item) {
      const route = this.$router.resolve({
        name: 'affairsCourseDetail',
        query: {
          courseId: item.courseCode,
          courseName: item.courseName,
          enterType: 'course'
        }
      })
      return route.href
    },
    goToDetail(row) {
      this.$refs['tableView'].clearSelection()
      this.setHighlightRow(row)
      this.$refs['tableView'].toggleRowSelection(row)
      localStorage.removeItem(`manageAffairs_resultList`)
      localStorage.setItem(`manageAffairs_resultList`, JSON.stringify(row.resultList))
      let str = ''
      if (row.schedulingType == '项目') {
        this.$router.push({
          name: 'listGroups',
          query: {
            classCode: row.classCode,
            schedulingCode: row.schedulingCode,
            curriculumCode: row.curriculumCode,
            content: row.content,
            sectionSeason: row.sectionSeason,
            sectionTime: row.sectionTime,
            answerTime: row.answerTime || 0
          }
        })
        return
      }
      if (row.curriculumType == '1' && row.schedulingType == '课程') {
        str = 'affairsTheory'
      } else if (row.curriculumType == '2' && row.schedulingType == '课程') {
        str = 'affairsSimulation'
      } else if (row.schedulingType == '考试') {
        str = 'affairsExamination'
      } else {
        str = 'affairsSimulation'
      }
      this.$router.push({
        name: str,
        query: {
          classStatus: row.classStatus,
          classCode: row.classCode,
          schedulingCode: row.schedulingCode,
          schedulingType: row.schedulingType,
          isExamMode: row.isExamMode,
          curriculumCode: row.curriculumCode,
          content: row.content,
          sectionSeason: row.sectionSeason,
          sectionTime: row.sectionTime,
          topologyAllocation: row.topologyAllocation,
          curriculumType: row.curriculumType,
          sceneInstanceId: row.sceneInstanceId,
          answerTime: row.answerTime || 0
        }
      })
    },
    getDetailHref(row) {
      localStorage.removeItem(`manageAffairs_resultList`)
      localStorage.setItem(`manageAffairs_resultList`, JSON.stringify(row.resultList))
      let str = ''
      if (row.schedulingType == '项目') {
        const route = this.$router.resolve({
          name: 'listGroups',
          query: {
            classCode: row.classCode,
            schedulingCode: row.schedulingCode,
            curriculumCode: row.curriculumCode,
            content: row.content,
            sectionSeason: row.sectionSeason,
            sectionTime: row.sectionTime,
            answerTime: row.answerTime || 0
          }
        })
        return route.href
      }
      if (row.curriculumType == '1' && row.schedulingType == '课程') {
        str = 'affairsTheory'
      } else if (row.curriculumType == '2' && row.schedulingType == '课程') {
        str = 'affairsSimulation'
      } else if (row.schedulingType == '考试') {
        str = 'affairsExamination'
      } else {
        str = 'affairsSimulation'
      }
      const route = this.$router.resolve({
        name: str,
        query: {
          classStatus: row.classStatus,
          classCode: row.classCode,
          schedulingCode: row.schedulingCode,
          schedulingType: row.schedulingType,
          isExamMode: row.isExamMode,
          curriculumCode: row.curriculumCode,
          content: row.content,
          sectionSeason: row.sectionSeason,
          sectionTime: row.sectionTime,
          topologyAllocation: row.topologyAllocation,
          curriculumType: row.curriculumType,
          sceneInstanceId: row.sceneInstanceId,
          answerTime: row.answerTime || 0
        }
      })
      return route.href
    }
  }
}
</script>
