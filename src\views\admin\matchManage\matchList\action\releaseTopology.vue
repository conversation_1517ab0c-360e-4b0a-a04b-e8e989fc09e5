<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">本操作将删除拓扑以及拓扑实例，请谨慎操作。</div>
    </el-alert>
    <el-alert :closable="false" class="resource-content" type="info">
      <div slot="title">
        <span class="mr-40">资源数量</span>
        <span>{{ num }}</span>
      </div>
    </el-alert>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="data[0].status != 1 && data[0].status != 6" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import batchTemplate from '@/packages/batch-delete/modal-bat-template'
import modalMixins from '@/packages/mixins/modal_form'
import { releaseMatchTopology } from '@/api/match/index.js'

export default {
  name: 'Finish',
  components: { batchTemplate },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    num: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: false,
      checked: false
    }
  },
  mounted() {
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      releaseMatchTopology(this.data[0].id).then(res => {
        this.$message.success('资源释放完成')
        this.$emit('call', 'refresh')
        this.close()
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
.resource-content {
  padding: 14px 16px;
}
</style>
