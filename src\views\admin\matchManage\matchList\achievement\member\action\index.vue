<template>
  <div class="buttons-wrap">
    <el-button :disabled="!parentVm.data.matchSceneInstanceVOList.length" type="primary" @click="clickDrop('setUpStage')">设置比例</el-button>
    <el-button type="primary" @click="clickDrop('exportReport')">导出</el-button>
    <el-button :disabled="multipleDisabled" type="primary" @click="clickDrop('cancel')">取消成绩</el-button>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import setUpStage from './set-up-stage.vue'
import exportReport from './modal-export'
import setAsPromotion from './modal-promotion.vue'
import cancel from './match-cancel.vue'

export default {
  components: {
    setUpStage,
    exportReport,
    setAsPromotion,
    cancel
  },
  mixins: [mixinsActionMenu],
  inject: ['parentVm'],
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'setUpStage': '设置比例',
        'setAsPromotion': '设为晋级',
        'exportReport': '导出',
        'cancel': '取消成绩'
      },
      dialogLoading: false
    }
  },
  mounted() {
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      } else if (type === 'setLoading') {
        this.dialogLoading = data
      }
    },
    clickDrop(name) {
      this.modalName = name
    }
  }
}
</script>
