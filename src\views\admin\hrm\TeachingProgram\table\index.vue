<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
        <el-checkbox v-model="selfCreated" style="margin-left: 30px;">仅查看自己创建</el-checkbox>
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索教案名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'name'">
            <a
              v-if="link"
              :href="getHref(scope.row)"
              @click.prevent="linkEvent('teachingPlanDetail', scope.row, {
              id: scope.row.id, name: scope.row.name, des: scope.row.description})">{{ scope.row.name || "-" }}
            </a>
          </span>
          <span v-else-if="item == 'coursePeriod'">{{ scope.row.coursePeriod + ' 课时' || "0" }}</span>
          <span v-else-if="item == 'privacy'">{{ scope.row.privacy === 0 ? '私有' : '公开' }}</span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { lessonPlanQuery } from '@/api/teacher/index.js'
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'name', label: '教案名称', placeholder: '请输入教案名称', master: true },
        { key: 'userName', label: '创建人', placeholder: '请输入创建人' }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'name': {
          title: '教案名称', master: true
        },
        'description': {
          title: '描述'
        },
        'coursePeriod': {
          title: '课时'
        },
        'privacy': {
          title: '使用权限'
        },
        'userName': {
          title: '创建人'
        },
        'createdAt': {
          title: '创建时间'
        }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'name',
        'description',
        'coursePeriod',
        'privacy',
        'userName',
        'createdAt'
      ],
      selfCreated: false,
      userId: null
    }
  },
  watch: {
    selfCreated() {
      this.getList()
    }
  },
  methods: {
    getList() {
      this.tableLoading = true
      const params = this.getPostData('page', 'limit')
      const data = {
        createdBy: this.selfCreated === false ? this.userId = null : this.userId = JSON.parse(localStorage.getItem('loginUserInfo')).userId,
        ...params
      }
      lessonPlanQuery(data).then((res) => {
        if (res.code === 0) {
          this.tableData = res.data.records
          this.tableTotal = res.data.total
        }
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    getHref(row) {
      const route = this.$router.resolve({
        name: 'teachingPlanDetail',
        query: {
          id: row.id,
          name: row.name,
          des: row.description
        }
      })
      return route.href
    }
  }
}
</script>



