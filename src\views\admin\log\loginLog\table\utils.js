const authResultArr = [
  { value: '1', label: '成功', type: 'success' },
  { value: '0', label: '失败', type: 'danger' }
]

export const searchKeyList = [
  { key: 'realname', label: '用户', master: true },
  {
    key: 'time_range',
    label: '时间',
    format: 'yyyy-MM-dd HH:mm:ss',
    type: 'time_range',
    placeholder: '选择时间范围'
  }
]
export const columnsObj = {
  'realname': { title: '用户', master: true },
  'loginTime': { title: '登录时间' },
  'ipAddress': { title: '客户端地址' },
  'loginAddress': { title: '登录地点' },
  'deviceType': { title: '设备类型' },
  'core': { title: '客户端内核' },
  'platform': { title: '客户端系统' },
  'authResult': { title: '认证结果' }
}
export const columnsViewArr = Object.keys(columnsObj)

export const authResultObj = authResultArr.reduce((acc, prev) => {
  acc[prev.value] = prev
  return acc
}, {})
