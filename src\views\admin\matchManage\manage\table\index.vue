<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索赛事名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="total"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'matchName'">
            <a
              v-if="link"
              :href="`/manage/match/matchDetail/${scope.row.id}/overview`"
              @click.prevent="linkEvent('matchDetail', scope.row, { id: scope.row.id, view: 'overview' })">{{ scope.row.matchName || '-' }}</a>
          </span>
          <span v-else-if="item == 'status'">
            <span>{{ scope.row.status == 1 ? '已发布' : '未发布' }}</span>
          </span>
          <span v-else-if="item == 'bigMatchSeasonNum'">
            <span>{{ scope.row.bigMatchSeasonNum ? scope.row.bigMatchSeasonNum : '-' }}</span>
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { queryBigMatch } from '@/api/match/index.js'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'matchName', label: '赛事名称', master: true, placeholder: '默认搜索赛事名称' },
        { key: 'status', label: '状态', type: 'radio',
          valueList: [{ label: '已发布', value: '1' }, { label: '未发布', value: '0' }]
        },
        { key: 'startTime', type: 'time_range', label: '开始时间' },
        { key: 'endTime', type: 'time_range', label: '结束时间' }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: module.columnsObj,
      total: 0,
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: module.columnsViewArr
    }
  },
  methods: {
    getList: function() {
      this.tableLoading = true
      const params = this.getPostData('page', 'limit')
      // 时间处理
      if (params.startTime) {
        const arr = params.startTime.split(',')
        params.startTimeBegin = arr[0]
        params.startTimeEnd = arr[1]
        delete params.startTime
      }
      if (params.endTime) {
        const arr = params.endTime.split(',')
        params.endTimeBegin = arr[0]
        params.endTimeEnd = arr[1]
        delete params.endTime
      }
      queryBigMatch(params).then(res => {
        if (res.code === 0) {
          this.tableLoading = false
          this.tableData = res.data.records
          this.total = Number(res.data.total)
          this.handleSelection()
        }
      })
    }
  }
}
</script>
