<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索考试名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      type="list"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'schedulingResName'">
            <a
              v-if="link"
              :href="getHref(scope.row)"
              @click.prevent="
                linkEvent('detailExamTraining', scope.row, {
                  id: scope.row.schedulingResId,
                  examName: scope.row.schedulingResName,
                  examStatus: scope.row.examStatus,
                  questionType: scope.row.schedulingObjType,
                  schedulingCode: scope.row.schedulingCode,
                  view: 'overview'
                })
              "
            >{{ scope.row.schedulingResName || "-" }}
            </a>
          </span>
          <span v-else-if="item == 'examStatus'">
            <span v-if="scope.row[item] == 'BEGIN'"><el-badge type="success" is-dot />进行中</span>
            <span v-if="scope.row[item] == 'PREPARE'"><el-badge type="info" is-dot />未开始</span>
            <span v-if="scope.row[item] == 'END'"><el-badge type="danger" is-dot />已结束</span>
          </span>
          <span v-else-if="item == 'beginTime'">
            <span>{{ scope.row.beginTime + ' 至 ' + scope.row.endTime }}</span>
          </span>
          <span v-else-if="item == 'regBeginTime'">
            <span>{{ scope.row.regBeginTime + ' 至 ' + scope.row.regEndTime }}</span>
          </span>
          <div v-else-if="item == 'studentName'">
            <div class="flex jc-between ai-center">
              <div class="ellipsis overflow-tooltip">
                <div v-if="scope.row.classCode" class="ellipsis">{{ scope.row.className || "-" }}</div>
                <div v-else class="ellipsis">{{ scope.row.userList[0] || "-" }}</div>
              </div>
              <CountPopover :list="scope.row.userList" />
            </div>
          </div>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
      <div slot="empty">
        <span>暂无数据</span>
        <el-tooltip effect="dark" content="请移至“教学事务”tab页签安排考试" placement="top">
          <i class="el-icon-warning-outline"/>
        </el-tooltip>
      </div>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import category from '../category/index.vue'
import CountPopover from '@/components/CountPopover/index'
import { queryClassSchedule } from '@/api/exam/index.js'

export default {
  components: {
    category,
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol,
    CountPopover
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'schedulingResName', label: '考试名称', master: true, placeholder: '默认搜索考试名称' },
        { key: 'className', label: '班级名称' },
        { key: 'studentName', label: '学员名称' }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: module.columnsObj,
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: module.columnsViewArr
    }
  },
  methods: {
    getList: function() {
      this.tableLoading = true
      const data = this.getPostData('page', 'limit')
      const params = {
        ...data,
        roleValue: ''
      }
      queryClassSchedule(params).then((res) => {
        if (res.code === 0) {
          this.tableData = res.data.records
          this.tableData.forEach(item => {
            item.userList = item.userList || []
          })
          this.tableTotal = res.data.total
        }
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    getHref(row) {
      const route = this.$router.resolve({
        name: 'detailExamTraining',
        params: {
          id: row.schedulingResId,
          examName: row.schedulingResName,
          examStatus: row.examStatus,
          questionType: row.schedulingObjType,
          schedulingCode: row.schedulingCode,
          view: 'overview'
        }
      })
      return route.href
    }
  }
}
</script>

<style scoped lang="scss">
.loadding {
  animation: fadenum 5s infinite;
}
@keyframes fadenum {
  100% {
    transform: rotate(360deg);
  }
}
</style>
