<template>
  <div v-loading="loading" class="dialog-wrap">
    <batch-template
      :data="data"
      :available-data="data"
      :show-delete-warning="true"
      :view-key="data.name"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!data.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { lessonPlanDetailQuery, lessonPlanHierarchyCourseDelete } from '@/api/teacher/index.js'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      hierarchyId: '',
      treeData: [],
      addItemId: '',
      grandsonNode: []
    }
  },
  created() {
    this.detailQuery()
  },
  methods: {
    // 获取方案详情
    detailQuery() {
      const params = {
        lessonPlanId: this.$route.query.id
      }
      lessonPlanDetailQuery(params).then(res => {
        this.treeData = [res.data]
        this.treeData.forEach(item => {
          item.type = 0
          if (item.children) {
            item.children.forEach(it => {
              it.type = 1
              if (it.children) {
                it.children.forEach(i => {
                  i.type = 2
                })
              }
            })
          }
        })
        this.filterAddItem()
        const ParentId = this.getParentIds(this.grandsonNode[0].id, this.treeData)
        this.hierarchyId = ParentId[1]
      })
    },
    // 查找新增体系节点
    filterAddItem() {
      this.treeData[0].children.forEach(item => {
        item.children.forEach(it => {
          if (it.name === this.data[0].name) {
            this.grandsonNode.push(it)
          }
        })
      })
    },
    // 查找父节点ID
    getParentIds(id, data) {
      function dfs(data, id, parents) {
        for (var i = 0; i < data.length; i++) {
          var item = data[i]
          if (item.id === id) return parents
          if (!item.children || !item.children.length) continue
          parents.push(item.id)
          if (dfs(item.children, id, parents).length) return parents
          parents.pop()
        }
        return []
      }
      return dfs(data, id, [])
    },
    close() {
      this.$emit('close')
    },
    confirm: function() {
      const params = {
        hierarchyId: this.hierarchyId,
        courseId: this.grandsonNode[0].id,
        lessonPlanId: this.$route.query.id
      }
      console.log('params', params)
      lessonPlanHierarchyCourseDelete(params).then((res) => {
        if (res.code === 0) {
          this.$message({
            message: '移除成功',
            type: 'success'
          })
          // 实时更新删除节点
          const nodeId = this.$parent.$parent.$refs.treeRef.getCurrentKey()
          const node = this.$parent.$parent.$refs.treeRef.getNode(nodeId)
          const parent = node.parent
          const children = parent.data.children || parent.data
          const index = children.findIndex(d => d.id === node.data.id)
          children.splice(index, 1)
          this.$emit('call', 'refresh')
          this.close()
        }
      }).catch(() => {
      })
    }
  }
}
</script>
