<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-radio v-model="type" label="search">按搜索结果导出</el-radio>
    <div style="padding: 10px 0px 10px 24px;">
      <el-row>
        <el-col :span="3">搜索项:</el-col>
        <el-col :span="21">
          <template v-if="searchView.length">
            <el-tag
              v-for="item in searchView"
              :key="item.key"
              class="ellipsis mr-5"
              style="max-width: 190px;"
              size="small"
            >
              <span v-if="item.key === 'sex'">
                {{ item.label }}：{{ item.value == 1 ? '男' : '女' }}
              </span>
              <span v-else>
                {{ item.label }}：{{ item.value }}
              </span>
            </el-tag>
          </template>
          <span v-else>无</span>
        </el-col>
      </el-row>
    </div>
    <el-radio v-model="type" label="all">导出全部</el-radio>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { formatDate } from '@/utils/index'
import { exportExcelFile } from '@/utils'
import axios from 'axios'
export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  inject: ['tableVm', 'parentVm'],
  props: {
    searchName: {
      type: Object
    }
  },
  data() {
    return {
      loading: false,
      type: 'search'
    }
  },
  computed: {
    'searchView': function() {
      const _data = []
      for (const key in this.tableVm.searchData) {
        _data.push({
          key: key,
          value: this.tableVm.searchData[key],
          label: this.tableVm.searchKeyList.find(item => item.key === key).label
        })
      }
      return _data
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const { detailTitle: name } = this.parentVm.data
      const params = Object.assign({}, this.tableVm.filterData, this.type === 'search' ? this.tableVm.searchData : {})
      params.examId = this.$route.params.id
      params.schedulingCode = this.$route.params.schedulingCode
      params.examName = name
      params.examStatus = this.searchName.examStatus
      axios({
        method: 'post',
        url: '/api/ca-exam/exam/studentListExport',
        data: params,
        responseType: 'blob'
      }).then((res) => {
        exportExcelFile(res, `${name}_考生列表_${formatDate(new Date(), 'yy-mm-dd')}`)
        this.close()
        this.loading = false
      })
    }
  }
}
</script>
