<template>
  <div>
    <el-tabs v-model="tabsActive" class="content-subs" type="card" style="margin-top: 20px;" @tab-click="handleTabClick">
      <el-tab-pane label="教学事务" name="teacherAffairs">
        <router-link :to="{ name: 'teacherAffairs' }" />
      </el-tab-pane>
      <el-tab-pane label="考试事务" name="TeachingAffairsExamTraining">
        <router-link :to="{ name: 'TeachingAffairsExamTraining' }" />
      </el-tab-pane>
      <el-tab-pane label="教学方案" name="teachingProgramRecord">
        <router-link :to="{ name: 'teachingProgramRecord' }" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    des: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tabsActive: ''
    }
  },
  created() {
    this.tabsActive = this.$route.name
  },
  methods: {
    'handleTabClick': function(data) {
      this.$router.push({ name: data.name })
    }
  }
}
</script>

