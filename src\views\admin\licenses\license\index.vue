<template>
  <div class="content-wrap-layout">
    <top-nav />
    <div class="licenses-box">
      <div v-if="licensesInfo" class="licenses-wrap">
        <div class="licenses-notice">
          <el-card>提示：下载授权码并将授权码文件发送给售后人员，由售后人员提供授权许可证书，通过“更新授权许可”功能升级证书！</el-card>
        </div>
        <el-row :gutter="15" type="flex" align="top" class="licenses-info">
          <el-col :span="8" class="licenses-left">
            <el-card style="height: 100%; position: relative;">
              <img class="box-con-img" src="@/assets/images/accept.png">
              <div class="box-con-text">{{ licensesInfo.type ? licenseType[licensesInfo.type] : '-' }}</div>
              <div class="licenses-logo">
                <svg t="1701264182796" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8658" width="200" height="200">
                  <path d="M523.776 86.016l-3.072-0.512v0.512L149.504 184.32l21.504 392.704c0 2.56 0 52.224 15.872 81.92 79.872 150.016 268.8 241.152 329.728 268.288 0 0 2.048 1.024 3.584 1.536v1.024c0.512 0 1.024-0.512 1.536-0.512 0.512 0 1.536 0.512 1.536 0.512v-1.024c1.536-0.512 2.56-1.536 3.072-1.536 60.416-27.136 249.856-118.272 329.728-268.288 15.872-29.696 17.408-79.36 17.408-81.92l22.016-392.704-371.712-98.304zM302.08 700.416V243.2h425.472v206.336c-13.312-7.68-27.648-13.312-42.496-16.896V283.136h-343.04v374.784h172.544c8.704 16.384 20.48 30.72 34.816 42.496H302.08z m193.536-109.056H400.384v-39.936h98.304c-2.048 10.24-3.072 20.992-3.072 31.744-0.512 3.072-0.512 5.632 0 8.192zM400.384 482.304v-39.936h185.344c-20.48 9.216-38.4 23.04-52.736 39.936H400.384z m0-109.056v-39.936h236.544v39.936H400.384z m250.88 346.112c-74.24 0-134.656-60.416-134.656-134.656s60.416-134.656 134.656-134.656 134.656 60.416 134.656 134.656-60.416 134.656-134.656 134.656z m0-233.472c-54.272 0-98.304 44.032-98.304 98.304s44.032 98.304 98.304 98.304 98.304-44.032 98.304-98.304-44.032-98.304-98.304-98.304z m46.08 151.552l-19.456-8.192-8.192 19.456-17.92-42.496-17.408 42.496-8.192-19.456-19.456 8.192 16.384-39.424-22.016-36.352 25.6-42.496h51.2l25.6 42.496-22.528 37.376 16.384 38.4z" fill="rgba(0, 113, 48, 0.8)" p-id="8659"/>
                </svg>
              </div>
              <h2 class="product-name">{{ licensesInfo.productName || productName }}</h2>
              <div class="product-name-small">{{ licensesInfo.productVersion || productVersion }}</div>
              <div style="padding-top: 10px;">
                <el-tag :type="licensesInfo.status === 'authorized' ? 'success' : 'danger'">{{ licenseStatus[licensesInfo.status] }}</el-tag>
              </div>
            </el-card>
          </el-col>
          <!-- 许可证信息卡片 -->
          <el-col :span="16">
            <el-card style="height: 100%;">
              <div class="licenses-title">
                <h2>许可证信息</h2>
                <el-button class="download-wrap" type="primary" @click="handleDownload">下载授权码</el-button>
                <el-button class="upload-wrap" type="primary" @click="clickDrop('UploadLicense')">更新授权许可</el-button>
              </div>
              <div class="licenses-content">
                <el-row>
                  <el-col :span="24">
                    <div class="th">产品序号</div>
                    <div class="td">
                      {{ licensesInfo.productSerialNumber || '-' }}
                    </div>
                  </el-col>
                  <el-col :span="24">
                    <div class="th">客户名称</div>
                    <div class="td">{{ licensesInfo.customerName || '-' }}</div>
                  </el-col>
                  <el-col :span="24">
                    <div class="th">证书类型</div>
                    <div class="td">{{ licensesInfo.type ? licenseType[licensesInfo.type] : '-' }}</div>
                  </el-col>
                  <el-col :span="24">
                    <div class="th">证书状态</div>
                    <div class="td">
                      <el-badge type="success" is-dot />
                      {{ `有效（${licenseStatus[licensesInfo.status]}）` }}
                    </div>
                  </el-col>
                  <el-col :span="24">
                    <div class="th">上传时间</div>
                    <div class="td">
                      <template v-if="licensesInfo.createTime!=='-1'">
                        {{ licensesInfo['createTime'] ? formatTime(licensesInfo['createTime']) : '-' }}
                      </template>
                      <template v-else>无限制</template>
                    </div>
                  </el-col>
                  <el-col :span="24">
                    <div class="th">生效时间</div>
                    <div class="td">
                      <template v-if="licensesInfo.issuedDate !== '-1'">
                        {{ licensesInfo['issuedDate'] ? formatTime(licensesInfo['issuedDate']) : '-' }}
                      </template>
                      <template v-else>无限制</template>
                    </div>
                  </el-col>
                  <el-col :span="24">
                    <div class="th">截止时间</div>
                    <div class="td">
                      <template v-if="licensesInfo.expiredDate !== '-1'">
                        {{ licensesInfo['expiredDate'] ? formatTime(licensesInfo['expiredDate']) : '-' }}
                      </template>
                      <template v-else>无限制</template>
                    </div>
                  </el-col>
                </el-row>
                <img class="licenses-img" src="@/assets/images/badge.png">
                <img class="licenses-ico" src="@/assets/images/logo_fav.png">
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <!-- 用户卡片 -->
          <el-col :span="8">
            <el-card class="card-item">
              <div class="card-item-title">
                <div class="title-top">
                  <div class="title-left">
                    <img :src="iconImg('user')" class="card-item-icon" alt="">
                    <b>用户 <small>[基础]</small></b>
                  </div>
                </div>
                <div class="card-item-info">平台负责管理的用户数量。</div>
              </div>
              <div class="card-item-content">
                <p><b>用户数</b><span>{{ licensesInfo.userNum ? licensesInfo.userNum === -1 ? '无限制' : (licensesInfo.usedUserNum + ' / ' + licensesInfo.userNum + ' ( 个 )') : '-' }}</span></p>
                <p><b>生效时间</b><span>{{ licensesInfo.issuedDate ? formatTime(licensesInfo.issuedDate) : '-' }}</span></p>
                <p><b>截止时间</b><span>{{ licensesInfo.expiredDate ? formatTime(licensesInfo.expiredDate) : '-' }}</span></p>
              </div>
            </el-card>
          </el-col>
          <!-- 维保卡片 -->
          <el-col :span="8">
            <el-card class="card-item">
              <div class="card-item-title">
                <div class="title-top">
                  <div class="title-left">
                    <img :src="iconImg('maintenance')" class="card-item-icon" alt="">
                    <b>维保 <small>[基础]</small></b>
                  </div>
                </div>
                <div class="card-item-info">平台的维保期限和状态，在维保期内提供运维服务。</div>
              </div>
              <div class="card-item-content">
                <p><b>状态</b><span>{{ maintenanceStatus[licensesInfo.maintenanceStatus] }}</span></p>
                <p><b>维保生效时间</b><span>{{ licensesInfo.maintenanceStartDate ? formatTime(licensesInfo.maintenanceStartDate) : '-' }}</span></p>
                <p><b>维保截止时间</b><span>{{ licensesInfo.maintenanceEndDate ? formatTime(licensesInfo.maintenanceEndDate) : '-' }}</span></p>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <div class="high-grade-title">业务模块 <small>[高级]</small></div>
        <el-row :gutter="15">
          <!-- 安全实训卡片 -->
          <el-col v-if="licensesInfo.modules.includes('training')" :span="8">
            <el-card class="card-item">
              <div class="card-item-title">
                <div class="title-top">
                  <div class="title-left">
                    <img :src="iconImg('practical-training')" class="card-item-icon" alt="">
                    <b>安全实训</b>
                  </div>
                </div>
                <div class="card-item-info">综合丰富地课程资源、实验环境，持续地、系统化地培训网络安全人才，快速、高效地完成知识技能转化。</div>
              </div>
              <div class="card-item-content">
                <p><b>生效时间</b><span>{{ licensesInfo.issuedDate ? formatTime(licensesInfo.issuedDate) : '-' }}</span></p>
                <p><b>截止时间</b><span>{{ licensesInfo.expiredDate ? formatTime(licensesInfo.expiredDate) : '-' }}</span></p>
              </div>
            </el-card>
          </el-col>
          <!-- 攻防演练卡片 -->
          <el-col v-if="licensesInfo.modules.includes('simulation')" :span="8">
            <el-card class="card-item">
              <div class="card-item-title">
                <div class="title-top">
                  <div class="title-left">
                    <img :src="iconImg('drill')" class="card-item-icon" alt="">
                    <b>攻防演练</b>
                  </div>
                </div>
                <div class="card-item-info">构建高仿真场景、编排任务“剧情”来进行单兵演练、团体演练，旨在提升用户的网络安全作战能力。</div>
              </div>
              <div class="card-item-content">
                <p><b>生效时间</b><span>{{ licensesInfo.issuedDate ? formatTime(licensesInfo.issuedDate) : '-' }}</span></p>
                <p><b>截止时间</b><span>{{ licensesInfo.expiredDate ? formatTime(licensesInfo.expiredDate) : '-' }}</span></p>
              </div>
            </el-card>
          </el-col>
          <!-- 实网演练卡片 -->
          <el-col v-if="licensesInfo.modules.includes('drills')" :span="8">
            <el-card class="card-item">
              <div class="card-item-title">
                <div class="title-top">
                  <div class="title-left">
                    <img :src="iconImg('drill')" class="card-item-icon" alt="">
                    <b>实网演练</b>
                  </div>
                </div>
                <div class="card-item-info">对实网攻防演练过程进行可视化及溯源分析，验证安全策略的有效性，提升相关人员的安全意识和应急响应能力。</div>
              </div>
              <div class="card-item-content">
                <p><b>生效时间</b><span>{{ licensesInfo.issuedDate ? formatTime(licensesInfo.issuedDate) : '-' }}</span></p>
                <p><b>截止时间</b><span>{{ licensesInfo.expiredDate ? formatTime(licensesInfo.expiredDate) : '-' }}</span></p>
              </div>
            </el-card>
          </el-col>
          <!-- 竞赛平台卡片 -->
          <el-col v-if="licensesInfo.modules.includes('match')" :span="8">
            <el-card class="card-item">
              <div class="card-item-title">
                <div class="title-top">
                  <div class="title-left">
                    <img :src="iconImg('competition')" class="card-item-icon" alt="">
                    <b>竞赛平台</b>
                  </div>
                </div>
                <div class="card-item-info">提供多种赛制场景，涉及到网络安全的各个细分领域，包括网络攻防、密码学、漏洞利用、逆向工程等。</div>
              </div>
              <div class="card-item-content">
                <p><b>生效时间</b><span>{{ licensesInfo.issuedDate ? formatTime(licensesInfo.issuedDate) : '-' }}</span></p>
                <p><b>截止时间</b><span>{{ licensesInfo.expiredDate ? formatTime(licensesInfo.expiredDate) : '-' }}</span></p>
              </div>
            </el-card>
          </el-col>
          <!-- 渗透测试管控平台卡片 -->
          <el-col v-if="licensesInfo.modules.includes('penetrant')" :span="8">
            <el-card class="card-item">
              <div class="card-item-title">
                <div class="title-top">
                  <div class="title-left">
                    <img :src="iconImg('permeate')" class="card-item-icon" alt="">
                    <b>渗透测试管控平台</b>
                  </div>
                </div>
                <div class="card-item-info">帮助企业用户实现对渗透测试人员、渗透测试工具、渗透测试行为的全过程管理控制、流量监测、安全审计以及溯源分析。</div>
              </div>
              <div class="card-item-content">
                <p><b>生效时间</b><span>{{ licensesInfo.issuedDate ? formatTime(licensesInfo.issuedDate) : '-' }}</span></p>
                <p><b>截止时间</b><span>{{ licensesInfo.expiredDate ? formatTime(licensesInfo.expiredDate) : '-' }}</span></p>
              </div>
            </el-card>
          </el-col>
          <!-- 漏洞复现卡片 -->
          <el-col v-if="licensesInfo.modules.includes('recurrent')" :span="8">
            <el-card class="card-item">
              <div class="card-item-title">
                <div class="title-top">
                  <div class="title-left">
                    <img :src="iconImg('recurrent')" class="card-item-icon" alt="">
                    <b>漏洞复现</b>
                  </div>
                </div>
                <div class="card-item-info">构建漏洞环境，以供安全人员发现、利用、分析、修复漏洞，加深对漏洞的理解</div>
              </div>
              <div class="card-item-content">
                <p><b>生效时间</b><span>{{ licensesInfo.issuedDate ? formatTime(licensesInfo.issuedDate) : '-' }}</span></p>
                <p><b>截止时间</b><span>{{ licensesInfo.expiredDate ? formatTime(licensesInfo.expiredDate) : '-' }}</span></p>
              </div>
            </el-card>
          </el-col>
          <!-- 检测管理卡片 -->
          <el-col v-if="licensesInfo.modules.includes('testing')" :span="8">
            <el-card class="card-item">
              <div class="card-item-title">
                <div class="title-top">
                  <div class="title-left">
                    <img :src="iconImg('detection')" class="card-item-icon" alt="">
                    <b>检测管理</b>
                  </div>
                </div>
                <div class="card-item-info">提供系统上线检测全流程管理，覆盖代码风险扫描、安全基线核查、信创检测、渗透测试等检测事项。</div>
              </div>
              <div class="card-item-content">
                <p><b>生效时间</b><span>{{ licensesInfo.issuedDate ? formatTime(licensesInfo.issuedDate) : '-' }}</span></p>
                <p><b>截止时间</b><span>{{ licensesInfo.expiredDate ? formatTime(licensesInfo.expiredDate) : '-' }}</span></p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <div v-loading="!licensesInfo" v-else class="h-100"/>
      <!--    中部弹窗 start-->
      <el-dialog
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :visible.sync="modalShow"
        :width="modalWidth"
        title="更新授权许可"
        @close="modalClose"
      >
        <transition name="el-fade-in-linear">
          <component
            :is="modalName"
            @success="upSuccess"
            @close="modalClose"
          />
        </transition>
      </el-dialog>
      <!--    中部弹窗 end-->
    </div>
  </div>
</template>

<script>
import topNav from './index_top_nav'
import UploadLicense from './UploadLicense'
import download from '@/packages/mixins/download'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import license from '../mixins/license'
export default {
  name: 'License',
  components: {
    topNav,
    UploadLicense
  },
  mixins: [download, mixinsActionMenu, license],
  created() {
    this.getData()
  },
  methods: {
    'upSuccess': function() {
      this.getData()
    },
    iconImg(icon) {
      const themeKey = window.ADMIN_CONFIG.THEME || 'green'
      return require('@/packages/assets/' + themeKey + '/' + icon + '.png')
    }
  }
}
</script>
<style lang="scss" scoped>
.licenses-box {
  position: relative;
  flex: 1;
  overflow-y: auto;
  .licenses-wrap {
    height: 100%;
    padding: 0 15px;
  }
  .high-grade-title {
    font-weight: bold;
    font-size: 18px;
    margin-left: 2px;
    margin-bottom: 15px;
    border-left: 5px solid var(--color-600);
    padding-left: 10px;
  }
  .licenses-notice {
    padding: 15px 0;
    font-size: 14px;
    /deep/ .el-card__body {
      padding: 15px 20px;
    }
  }
  .box-con-img {
    position: absolute;
    top: 9px;
    left: 7px;
    width: 64px;
    height: 54px;
  }
  .box-con-text {
    color: rgba(0, 113, 48, 1);
    font-size: 13px;
    position: absolute;
    top: 27px;
    left: 19px;
    -webkit-transform: rotate(-10deg);
    -moz-transform: rotate(-10deg);
    -ms-transform: rotate(-10deg);
    -o-transform: rotate(-10deg);
  }
  .card-item {
    /deep/ &.el-card {
      margin-bottom: 15px;
    }
    .card-item-icon {
      width: 18px;
      height: 18px;
      // line-height: 28px;
      // font-size: 24px;
      margin-right: 5px;
    }
    .title-left {
      display: flex;
      align-items: center;
      font-size: 18px;
      height: 34px;
      line-height: 34px;
      & > b {
        line-height: 32px;
        display: inline-block;
      }
    }
    .card-item-info {
      margin: 15px 0;
      height: 48px;
      color: #999;
      font-size: 14px;
      line-height: 1.2;
      border-bottom: 1px dotted rgb(221, 222, 226);
    }
    .card-item-content {
      & > p {
        font-size: 14px;
        padding: 6px 0;
        color: #444;
        & > b {
          display: inline-block;
          width: 120px;
        }
      }
    }
  }
  .licenses-logo {
    margin: 10px auto 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    svg {
      width: 150px;
      height: 150px;
    }
  }
  .licenses-title {
    position: relative;
    border-bottom: 1px dotted rgb(221, 222, 226);
    padding-bottom: 10px;
    margin-bottom: 10px;
    h2 {
      font-size: 18px;
      line-height: 32px;
      height: 32px;
    }
    .upload-wrap {
      margin-top: 0px;
      position: absolute;
      right: 0;
      top: 0;
    }
    .download-wrap {
      position: absolute;
      right: 120px;
      top: 0;
    }
  }
  .licenses-content {
    display: flex;
    align-items: center;
    position: relative;
    .licenses-img {
      width: 120px;
      height: 162px;
      margin-right: 40px;
    }
    .licenses-ico {
      position: absolute;
      right: 68px;
      top: 63px;
    }
  }
  .licenses-info {
    align-items: stretch;
    margin-bottom: 15px;
    .licenses-left {
      text-align: center;
    }
    .product-name {
      font-size: 18px;
    }
    .product-name-small {
      margin-top: 10px;
      font-size: 12px;
    }
    .th {
      display: inline-block;
      font-weight: 500;
      width: 120px;
      font-size: 14px;
      padding: 6px 5px 6px 0;
    }
    .td {
      display: inline-block;
      padding: 6px 5px 6px 0;
      font-size: 14px;
    }
  }
}
</style>
