<template>
  <div class="buttons-wrap">
    <el-button type="primary" icon="el-icon-plus" @click="clickDrop('modalAdd')">创建标签</el-button>
    <el-button :disabled="singleDisabled" type="primary" @click="clickDrop('modalModify')">编辑</el-button>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :label-id="labelId"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>

<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import modalAdd from './modal-add'
import modalModify from './modal-add'

export default {
  components: {
    modalAdd,
    modalModify
  },
  mixins: [mixinsActionMenu],
  props: {
    labelId: {
      type: String | Number,
      default: ''
    }
  },
  data() {
    return {
      importShow: false,
      // 弹窗title映射
      titleMapping: {
        'modalAdd': '创建标签',
        'modalModify': '编辑'
      }
    }
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    }
  }
}
</script>
