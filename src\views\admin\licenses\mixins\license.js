import { timeToFormatTime } from '@/utils'
import { getLicenseInfo } from '@/api/admin/licenses'
export default {
  data() {
    return {
      productName: '网络安全云靶场',
      productVersion: 'v2.0.0',
      licensesInfo: null,
      maintenanceStatus: {
        'unauthorized': '已过保',
        'authorized': '维保中',
        'expired': '已过保'
      },
      licenseStatus: {
        'unauthorized': '尚未授权',
        'authorized': '已授权',
        'expired': '授权过期'
      },
      licenseType: {
        'taial': '试用版',
        'enterprise': '企业版',
        'poc': 'POC版',
        'test': '测试版',
        'develop': '开发版',
        'generic': '普通版',
        'privileged': '专属版'
      },
      highGradeMap: {
        'training': '安全实训',
        'simulation': '仿真演练',
        'drills': '实网演练',
        'match': '竞赛平台',
        'testing': '检测管理',
        'penetrant': '渗透测试管控平台',
        'recurrent': '漏洞复现'
      }
    }
  },
  methods: {
    getData() {
      return new Promise((resolve, reject) => {
        getLicenseInfo().then((res) => {
          if (res.code == 0) {
            this.licensesInfo = res.data
            resolve(res.data)
          }
        }).catch(() => {
          reject(null)
        })
      })
    },
    handleDownload() {
      window.open(window.ADMIN_CONFIG.BASE_API + 'license/getLicenseKey')
    },
    formatTime(time) {
      return timeToFormatTime(time, 'YYYY-MM-DD HH:mm:ss')
    }
  }
}
