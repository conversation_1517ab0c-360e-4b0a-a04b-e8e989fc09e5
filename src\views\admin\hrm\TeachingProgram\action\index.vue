<template>
  <div class="buttons-wrap">
    <el-button type="primary" icon="el-icon-plus" @click="clickDrop('addProgram')">创建</el-button>
    <el-dropdown trigger="click" placement="bottom-start" @command="clickDrop">
      <el-button type="primary">
        操作<i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item :disabled="multipleDisabled" command="deleteProgram">删除</el-dropdown-item>
        <el-dropdown-item :disabled="singleDisabled" command="editProgram">编辑</el-dropdown-item>
        <el-dropdown trigger="click" placement="right-start" @command="clickDrop">
          <el-dropdown-item :disabled="multipleDisabled">设置使用权限</el-dropdown-item>
          <el-dropdown-menu slot="dropdown" class="dropdown-menu">
            <el-dropdown-item command="open">公开</el-dropdown-item>
            <el-dropdown-item command="privately">私有</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 弹窗 -->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
  </div>
</template>
<script>
import addProgram from './modal-add.vue'
import editProgram from './modal-edit.vue'
import deleteProgram from './modal-delete.vue'
import open from './modal-auth.vue'
import privately from './modal-auth.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
export default {
  components: {
    addProgram,
    editProgram,
    deleteProgram,
    open,
    privately
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'addProgram': '创建教学方案',
        'deleteProgram': '删除教学方案',
        'editProgram': '编辑教学方案',
        'open': '设为公开',
        'privately': '设为私有'
      },
      confirmDisabled: false
    }
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    }
  }
}
</script>
