<template>
  <div class="category-wrap">
    <transverse-list
      :data="examStatusArr"
      :allow-deletion="false"
      :allow-add="false"
      :all="true"
      :module-name="categoryName + '_' + moduleName + '_type'"
      :cache-pattern="true"
      :is-show-expand="false"
      v-bind="categoryProps"
      title="考试状态"
      @node-click="handleNodeClick($event, 'examStatus')"
    />
  </div>
</template>
<script>
import transverseList from '@/packages/transverse-list/index.vue'
import module from '../config.js'

export default {
  components: {
    transverseList
  },
  props: {
    examStatus: [String, Number],
    examFinish: [String, Number],
    publicStatus: [String, Number],
    categoryName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      moduleName: module.name,
      examStatusArr: module.examStatus,
      categoryProps: {
        label: 'label',
        idName: 'value'
      }
    }
  },
  mounted() {
  },
  methods: {
    handleNodeClick(item, key) {
      console.log('item, key', item, key)
      this.examStatus = item.value
      this.$emit('category-query', this.examStatus)
    }
  }
}
</script>
