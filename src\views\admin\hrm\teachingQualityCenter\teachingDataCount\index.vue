<template>
  <div class="content-wrap-layout">
    <top-nav />
    <div class="top-ranking-wrap">
      <el-row :gutter="15">
        <el-col :span="8">
          <el-card>
            <div class="chart-title">课程学习次数top5</div>
            <div v-if="studyNumList.length > 0" class="count-container">
              <div v-for="(item, index) in studyNumList" :key="index" class="count-div">
                <div :title="item.content" class="course-name">
                  {{ item.content }}
                </div>
                <div :title="item.createdBy" class="course-by">
                  {{ item.createdBy }}
                </div>
                <el-progress :show-text="false" :stroke-width="17" :percentage="item.percentage" class="progress" />
                <div :title="`${item.num}次`" class="count">{{ item.num }}次</div>
              </div>
            </div>
            <div v-else class="noData">暂无数据</div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card>
            <div class="chart-title">模拟练习次数top5</div>
            <div v-if="testPaperNumList.length > 0" class="count-container">
              <div v-for="(item, index) in testPaperNumList" :key="index" class="count-div">
                <div :title="item.content" class="course-name">
                  {{ item.content }}
                </div>
                <div :title="item.createdBy" class="course-by">
                  {{ item.createdBy }}
                </div>
                <el-progress :show-text="false" :stroke-width="17" :percentage="item.percentage" class="progress" />
                <div :title="`${item.num}次`" class="count">{{ item.num }}次</div>
              </div>
            </div>
            <div v-else class="noData">暂无数据</div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card>
            <div class="chart-title">教案使用次数top5</div>
            <div v-if="teachingPlanNumList.length > 0" class="count-container">
              <div v-for="(item, index) in teachingPlanNumList" :key="index" class="count-div">
                <div :title="item.content" class="course-name">
                  {{ item.content }}
                </div>
                <div :title="item.createdBy" class="course-by">
                  {{ item.createdBy }}
                </div>
                <el-progress :show-text="false" :stroke-width="17" :percentage="item.percentage" class="progress" />
                <div :title="`${item.num}次`" class="count">{{ item.num }}</div>
              </div>
            </div>
            <div v-else class="noData">暂无数据</div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <div class="bottom-lines">
      <el-row :gutter="15">
        <el-col :span="12">
          <el-card>
            <div class="line-head">
              <span>班级模拟练习得分统计top5</span>
              <div class="line-head-center">
                <span>最近</span>
                <el-input v-model.trim="classMonth" :min="1" :max="12" class="month-num" size="mini" type="number" @blur="getClassTestScoreRanking(classMonth)" />
                <span>个月内</span>
                <el-button type="primary" icon="el-icon-refresh" @click="getClassTestScoreRanking(12)" />
              </div>
            </div>
            <lineChart v-if="classTestScoreList.length" :x-axis="classXAxisMonth" :data="classTestScoreList" :y-axis-name="'班级模拟练习得分'" class="bottom-chart" />
            <div v-else class="noData echarts-noData">暂无数据</div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <div class="line-head">
              <span>学员模拟练习得分统计top5</span>
              <div class="line-head-center">
                <span>最近</span>
                <el-input v-model.trim="studentMonth" :min="1" :max="12" class="month-num" size="mini" type="number" @blur="getStudentTestScoreRanking(studentMonth)" />
                <span>个月内</span>
                <el-button type="primary" icon="el-icon-refresh" @click="getStudentTestScoreRanking(12)" />
              </div>
            </div>
            <lineChart v-if="studentTestScoreList.length" :x-axis="studentXAxisMonth" :data="studentTestScoreList" :y-axis-name="'学员模拟练习得分'" class="bottom-chart" />
            <div v-else class="noData echarts-noData">暂无数据</div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import moduleConf from './config'
import topNav from '../index_top_nav'
import { studyNumRanking, testPaperNumRanking, teachingPlanNumRanking, classTestScoreRanking, studentTestScoreRanking } from '@/api/admin/training/teachingQuality'
import lineChart from '@/components/Echarts/lineChart.vue'

export default {
  components: {
    topNav,
    lineChart
  },
  data() {
    return {
      moduleName: moduleConf.name,
      classMonth: 12,
      studentMonth: 12,
      studyNumList: [],
      testPaperNumList: [],
      teachingPlanNumList: [],
      classTestScoreList: [],
      classXAxisMonth: [],
      studentTestScoreList: [],
      studentXAxisMonth: []
    }
  },
  created() {
    this.init()
  },
  methods: {
    // 初始化所有图表
    init() {
      this.getStudyNumRanking()
      this.getTestPaperNumRanking()
      this.getTeachingPlanNumRanking()
      this.getClassTestScoreRanking(12)
      this.getStudentTestScoreRanking(12)
    },
    // 获取课程学习次数排名
    getStudyNumRanking() {
      studyNumRanking({}).then((res) => {
        if (res.code === 0 && res.data) {
          this.studyNumList = res.data || []
          const maxNum = this.studyNumList.reduce((prev, current) => (prev.num > current.num) ? prev : current).num
          this.studyNumList.map(item => {
            item.percentage = Math.round((item.num / maxNum) * 100)
          })
        }
      })
    },
    // 获取模拟练习次数排名
    getTestPaperNumRanking() {
      testPaperNumRanking({}).then((res) => {
        if (res.code === 0 && res.data) {
          this.testPaperNumList = res.data || []
          const maxNum = this.testPaperNumList.reduce((prev, current) => (prev.num > current.num) ? prev : current).num
          this.testPaperNumList.map(item => {
            item.percentage = Math.round((item.num / maxNum) * 100)
          })
        }
      })
    },
    // 获取教案使用次数排名
    getTeachingPlanNumRanking() {
      teachingPlanNumRanking({}).then((res) => {
        if (res.code === 0 && res.data) {
          this.teachingPlanNumList = res.data || []
          const maxNum = this.teachingPlanNumList.reduce((prev, current) => (prev.num > current.num) ? prev : current).num
          this.teachingPlanNumList.map(item => {
            item.percentage = Math.round((item.num / maxNum) * 100)
          })
        }
      })
    },
    // 获取班级模拟练习得分统计
    getClassTestScoreRanking(month) {
      if (Number(month) < 1 || Number(month) > 12) {
        this.$message.warning('月份只允许输入1~12')
        this.classMonth = ''
        return
      }
      this.classMonth = month
      classTestScoreRanking({ month }).then(res => {
        if (res.code === 0 && res.data) {
          this.classTestScoreList = res.data.testScoreVOList || []
          this.classTestScoreList.map(item => {
            item.avgScoreList.reverse()
          })
          this.classXAxisMonth = res.data.date.reverse()
        }
      })
    },
    // 获取学生模拟练习得分统计
    getStudentTestScoreRanking(month) {
      if (Number(month) < 1 || Number(month) > 12) {
        this.$message.warning('月份只允许输入1~12')
        this.studentMonth = ''
        return
      }
      this.studentMonth = month
      studentTestScoreRanking({ month }).then(res => {
        if (res.code === 0 && res.data) {
          this.studentTestScoreList = res.data.testScoreVOList || []
          this.studentTestScoreList.map(item => {
            item.avgScoreList.reverse()
          })
          this.studentXAxisMonth = res.data.date.reverse()
        }
      })
    }
  }
}
</script>
<style lang="scss">
.top-ranking-wrap {
  padding: 20px;
  .chart-title {
    font-family: Source Han Sans CN;
    font-size: 16px;
    color: #333;
  }
  .empty-container {
    height: 220px;
  }
  .count-container {
    height: 220px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    .count-div {
      margin: 10px 0;
      display: flex;
      justify-content: space-between;
      .course-name {
        width: 20%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .progress {
        width: 50%;
      }
      .course-by {
        width: 15%;
        margin: 0 10px 0 10px;
        text-align: left;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .count {
        width: 8%;
        text-align: right;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
.bottom-lines {
  margin: 0 20px;
  .line-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: Source Han Sans CN;
    font-size: 16px;
    color: #333;
    .month-num {
      width: 60px !important;
    }
  }
  .bottom-chart {
    height: 280px;
  }
}
.noData{
  height: 220px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
}
.echarts-noData{
  height: 280px;
}
</style>
