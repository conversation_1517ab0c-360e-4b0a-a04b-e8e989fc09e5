<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">当比赛状态变为"错误"且问题修复后，可以通过此功能重置比赛状态。</div>
    </el-alert>
    <el-form ref="ruleForm" :model="formData" :rules="rules" label-position="left" label-width="80px">
      <el-form-item label="状态" prop="status" placeholder="请选择">
        <el-select v-model="formData.status" style="width: 300px;">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="resetForm('ruleForm')">取消</el-button>
      <el-button type="primary" @click="submitForm('ruleForm')">确定</el-button>
    </div>
  </div>
</template>

<script>
import modalMixins from '@/packages/mixins/modal_form'
import { resetMatchStatusApi } from '@/api/match/index.js'

export default {
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      formData: {
        status: ''
      },
      statusOptions: [
        { label: '未就绪', value: 0 },
        { label: '待开赛', value: 1 },
        { label: '进行中', value: 2 },
        { label: '已暂停', value: 4 },
        { label: '已结束', value: 6 },
        { label: '错误', value: 8 }
      ],
      rules: {
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.close()
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            id: this.data[0].id,
            status: this.formData.status
          }
          this.loading = true
          resetMatchStatusApi(params).then((res) => {
            if (res.code === 0 || res.code === 200) {
              this.loading = false
              this.$message.success('重置成功')
              this.$emit('call', 'refresh')
              this.close()
            }
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },
    resetForm(formName) {
      this.close()
      this.$refs[formName].resetFields()
    }
  }
}
</script>
