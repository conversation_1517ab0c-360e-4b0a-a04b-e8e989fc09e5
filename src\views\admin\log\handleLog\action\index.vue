<template>
  <div class="buttons-wrap">
    <el-button slot="action" type="primary" @click="exportClick()">导出</el-button>
  </div>
</template>
<script>
import _ from 'lodash'
import { handleLogExportAPI } from '@/api/admin/log'
import { Loading } from 'element-ui'
import { downloadExcelWithResData } from '@/utils'
export default {
  inject: ['tableVm'],
  methods: {
    exportClick() {
      const loading = Loading.service({ fullscreen: true, text: '导出中...' })
      const params = this.tableVm.getPostData()
      if ('behaviorResult' in params) {
        params.behaviorResult = _.cloneDeep(params).behaviorResult == 1
      }
      handleLogExportAPI(params).then(res => {
        downloadExcelWithResData(res)
      }).finally(() => {
        loading.close()
      })
    }
  }
}
</script>
