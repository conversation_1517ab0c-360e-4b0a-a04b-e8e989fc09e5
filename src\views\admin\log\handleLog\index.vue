<template>
  <div class="content-wrap-layout">
    <page-table
      ref="table"
      :filter-data="{logType: 1}"
      :default-selected-arr="defaultSelectedArr"
      :cache-pattern="true"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
    >
      <action-menu
        slot="action"
        :module-name="moduleName"
        :select-item="selectItem"
        @call="actionHandler"
      />
    </page-table>
  </div>
</template>

<script>
import moduleConf from './config'
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
export default {
  // 操作日志
  name: moduleConf.name,
  components: {
    pageTable,
    actionMenu
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: []
    }
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {}
  }
}
</script>
