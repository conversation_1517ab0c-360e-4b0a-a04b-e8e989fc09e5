<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-radio v-removeAriaHidden v-model="type" label="search">按搜索结果导出</el-radio>
    <div style="padding: 10px 0px 10px 24px;">
      <el-row>
        <el-col :span="3">搜索项:</el-col>
        <el-col :span="21">
          <template v-if="searchView.length">
            <el-tag
              v-for="item in searchView"
              :key="item.key"
              class="ellipsis mr-5"
              style="max-width: 190px;"
              size="small"
            >
              <span>
                {{ item.label }}：{{ item.value }}
              </span>
            </el-tag>
          </template>
          <span v-else>无</span>
        </el-col>
      </el-row>
    </div>
    <el-radio v-removeAriaHidden v-model="type" label="all">导出全部</el-radio>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template'
import modalMixins from '@/packages/mixins/modal_form'
import { exportSeasonTeamScore } from '@/api/match/session.js'
import { downloadExcelWithResData } from '@/utils'

export default {
  name: 'Finish',
  components: { batchTemplate },
  mixins: [modalMixins],
  inject: ['tableVm'],
  data() {
    return {
      loading: false,
      type: 'search'
    }
  },
  computed: {
    'searchView': function() {
      const _data = []
      for (const key in this.tableVm.searchData) {
        _data.push({
          key: key,
          value: this.tableVm.searchData[key],
          label: this.tableVm.searchKeyList.find(item => item.key === key).label
        })
      }
      if (this.tableVm.camp) {
        _data.push({
          key: 'camp',
          value: this.tableVm.camp == 1 ? '红方' : '蓝方',
          label: '阵营'
        })
      }
      return _data
    }
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const params = Object.assign({}, this.tableVm.filterData, this.type === 'search' ? this.tableVm.searchData : {})
      if (this.type === 'search') {
        params.camp = this.tableVm.camp ? this.tableVm.camp : ''
      }
      params.bigMatchSeasonId = this.$route.params.id
      params.promotionIs = false
      exportSeasonTeamScore(params).then((res) => {
        this.$message.success('比赛战队成绩导出成功')
        downloadExcelWithResData(res)
      })
      this.close()
    }
  }
}
</script>
