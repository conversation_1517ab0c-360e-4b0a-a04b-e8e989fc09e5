<template>
  <div>
    <el-tabs v-model="tabsActive" class="content-subs" type="card" style="margin-top: 20px;" @tab-click="handleTabClick">
      <el-tab-pane label="教学数据统计" name="teachingDataCount">
        <router-link :to="{ name: 'teachingDataCount' }" />
      </el-tab-pane>
      <el-tab-pane label="班级能力统计" name="classAbilityCount">
        <router-link :to="{ name: 'classAbilityCount' }" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tabsActive: ''
    }
  },
  created() {
    this.tabsActive = this.$route.name
  },
  methods: {
    'handleTabClick': function(data) {
      this.$router.push({ name: data.name })
    }
  }
}
</script>
