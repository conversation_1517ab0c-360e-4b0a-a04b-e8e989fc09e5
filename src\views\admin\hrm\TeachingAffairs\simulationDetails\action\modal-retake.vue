<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        <p>
          1.仅课程内容随堂练习为考试模式时，支持学员重考;<br >
          2.学员设置重考后将清理学员之前的答题记录，请谨慎操作！
        </p >
      </div>
    </el-alert>
    <batch-template
      :data="data"
      :available-data="availableData"
      post-key="userId"
      view-key="userName"
    />
    <el-checkbox v-model="checked">我已知晓上述风险</el-checkbox>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!checked || !availableData.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { practiceAnswerDetailsResit } from '@/api/teacher/index.js'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      checked: false
    }
  },
  computed: {
    availableData: function() {
      const tempArr = this.data.filter((item) => {
        return item.reviewState == 1 // 已阅卷可以重考
      })
      return tempArr
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const affairDetailsVoList = this.availableData.map(item => {
        const { userId, classCode, schedulingCode, evaluationCode } = item
        return {
          userId,
          classCode,
          schedulingCode,
          evaluationCode
        }
      })
      practiceAnswerDetailsResit(affairDetailsVoList).then(res => {
        if (res.code == 0) {
          this.$message.success('学员设为重考成功')
          this.$emit('call', 'refresh')
          this.close()
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
