<template>
  <div class="content-wrap-layout">
    <top-nav />
    <!-- 分类区 -->
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      :cache-pattern="true"
      :module-name="moduleName"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
    >
      <action-menu
        slot="action"
        :type="2"
        :module-name="moduleName"
        :select-item="selectItem"
        @call="actionHandler"
      />
    </page-table>
  </div>
</template>

<script>
import moduleConf from './config'
import topNav from '../index_top_nav'
import pageTable from '../table/exampleTable'
import actionMenu from '../action/index'
export default {
  components: {
    topNav,
    pageTable,
    actionMenu
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      categoryId: ''
    }
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {}
  }
}
</script>
