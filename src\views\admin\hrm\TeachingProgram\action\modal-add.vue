<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="80px">
      <el-form-item label="名称" prop="name">
        <el-input v-model.trim="formData.name" maxlength="64"/>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
          :autosize="{ minRows: 4, maxRows: 4 }"
          v-model.trim="formData.description"
          resize="none"
          type="textarea"
          maxlength="255"
        />
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import validate from '@/packages/validate'
import { lessonPlanSave } from '@/api/teacher/index.js'
export default {
  components: {},
  mixins: [],
  inject: ['tableVm'],
  props: {
  },
  data() {
    return {
      moduleName: module.name,
      loading: false,
      validate: validate,
      formData: {
        name: '',
        description: ''
      },
      rules: {
        name: [validate.required(), validate.base_name],
        description: [validate.required(), validate.description]
      }
    }
  },
  created() {
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const params = this.formData
          params.source = 'back'
          lessonPlanSave(params).then((res) => {
            if (res.code === 0) {
              this.$message({
                message: '创建成功',
                type: 'success'
              })
              this.$emit('call', 'refresh')
              this.close()
            }
          }).catch(() => {
            this.loading = false
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>


