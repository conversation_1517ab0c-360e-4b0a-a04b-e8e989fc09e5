<template>
  <div class="buttons-wrap">
    <!-- 审批状态为“已通过”且是否确认为“待确认” -->
    <el-button
      v-permission="'manage.testing.application.finishApplication.applicationConfirmId'"
      :disabled="singleDisabled || !(selectItem[0] && selectItem[0].confirm == 0 && selectItem[0].status == 1)"
      type="primary"
      @click="handleConfirm"
    >
      确认
    </el-button>
    <!-- 已通过且未关联项目高亮关联检测项目 -->
    <el-button
      v-permission="'manage.testing.application.finishApplication.applicationProjectBind'"
      :disabled="singleDisabled || canRelate"
      type="primary"
      @click="handleRelateProject"
    >
      关联检测项目
    </el-button>
    <el-button
      v-permission="'manage.testing.application.unApplication.unApplicationList.applicationDelete'"
      :disabled="multipleDisabled"
      type="primary"
      @click="handleDelete()"
    >
      删除
    </el-button>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>

<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import confirm from './modal-confirm'
import deleteApplication from './modal-delete'
import relate from './modal-relate'

export default {
  name: 'TestingApplicationPassedAction',
  components: {
    deleteApplication,
    confirm,
    relate
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      modalWidth: '520px',
      // 弹窗title映射
      titleMapping: {
        'deleteApplication': '删除',
        'confirm': '确认',
        'relate': '关联检测项目'
      }
    }
  },
  computed: {
    canRelate: function() {
      if (this.selectItem[0] && this.selectItem[0].status === 1) {
        if (this.selectItem[0] && this.selectItem[0].projectId) {
          return true
        } else {
          return false
        }
      } else {
        return true
      }
    }
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      } else if (type === 'openRelate') {
        // 关闭当前弹窗
        this.modalClose()
        // 打开关联检测项目的弹窗
        setTimeout(() => {
          this.handleRelateProject()
        }, 100)
      }
    },
    handleConfirm() {
      this.modalName = 'confirm'
    },
    handleRelateProject() {
      this.modalName = 'relate'
    },
    handleDelete() {
      this.modalName = 'deleteApplication'
    }
  }
}
</script>

<style lang="scss" scoped>
.action-menu {
  display: flex;
  gap: 10px;
}
</style>
