<template>
  <div class="resource-table">
    <div class="operation-wrap">
      <div class="operation-left">
        <el-button :loading="exportLoading" type="primary" @click="handleExport">导出</el-button>
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
    </div>
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :multiple-page="multiplePage"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      type="list"
      current-key="userId"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        :show-overflow-tooltip="item !== 'modules'"
      >
        <template slot-scope="scope">
          <!-- 证书类型 -->
          <span v-if="item === 'type'">{{ scope.row[item] ? licenseType[scope.row[item]] : '-' }}</span>
          <!-- 证书状态 -->
          <span v-else-if="item === 'status'">
            <el-badge :type="scope.$index === 0 ? 'success' : 'danger'" is-dot />
            {{ showStatusText(scope.$index, scope.row) }}
          </span>
          <!-- 上传时间、生效时间、截止时间 -->
          <span v-else-if="item === 'createTime' || item === 'issuedDate' || item === 'expiredDate' || item === 'maintenanceStartDate' || item === 'maintenanceEndDate'">
            {{ scope.row[item] ? formatTime(scope.row[item]) : '-' }}
          </span>
          <!-- 用户数 -->
          <span v-else-if="item === 'userNum'">
            {{ scope.row[item] ? scope.row[item] === -1 ? '无限制' : (scope.row[item] + ' ( 个 )') : '-' }}
          </span>
          <!-- 维保天数 -->
          <span v-else-if="item === 'maintenanceDays'">{{ scope.row[item] }}</span>
          <!-- 业务模块 -->
          <span v-else-if="item === 'modules'">
            <table-td-multi-col :data="scope.row.modules" :number="scope.row.modules.length">
              <div slot="reference">{{ highGradeMap[scope.row.modules[0]] }}</div>
              <div v-for="val in scope.row.modules" :key="val">{{ highGradeMap[val] }}</div>
            </table-td-multi-col>
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import XLSX from 'xlsx'
import module from '../config.js'
import tTableView from '@/packages/table-view/index.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { licenseHistory } from '@/api/admin/licenses'
import license from '../../mixins/license'
export default {
  name: 'History',
  components: {
    tTableView,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable, license],
  data() {
    return {
      exportLoading: false,
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'customerName': { title: '客户名称', master: true },
        'type': { title: '证书类型' },
        'status': { title: '证书状态' },
        'createTime': { title: '上传时间' },
        'issuedDate': { title: '生效时间' },
        'expiredDate': { title: '截止时间' },
        'userNum': { title: '用户数' },
        'maintenanceStartDate': { title: '维保生效时间' },
        'maintenanceEndDate': { title: '维保截止时间' },
        'modules': { title: '业务模块' }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'customerName',
        'type',
        'status',
        'createTime',
        'issuedDate',
        'expiredDate',
        'userNum',
        'maintenanceStartDate',
        'maintenanceEndDate',
        'modules'
      ]
    }
  },
  methods: {
    handleExport() {
      this.exportLoading = true
      const xlsxData = []
      this.tableData.forEach((item, index) => {
        xlsxData.push({
          '产品': item.productName || this.productName,
          '客户名称': item.customerName || '-',
          '证书类型': item.type ? this.licenseType[item.type] : '-',
          '证书状态': this.showStatusText(index, item),
          '上传时间': item.createTime ? this.formatTime(item.createTime) : '-',
          '生效时间': item.issuedDate ? this.formatTime(item.issuedDate) : '-',
          '截止时间': item.expiredDate ? this.formatTime(item.expiredDate) : '-',
          '用户数': item.userNum === -1 ? '无限制' : (item.userNum + ' ( 个 )'),
          '维保生效时间': item.maintenanceStartDate ? this.formatTime(item.maintenanceStartDate) : '-',
          '维保截止时间': item.maintenanceEndDate ? this.formatTime(item.maintenanceEndDate) : '-',
          '业务模块': item.modules && item.modules.length ? item.modules.map(val => this.highGradeMap[val]).join(',') : '-'
        })
      })
      /* 创建worksheet */
      var ws = XLSX.utils.json_to_sheet(xlsxData)
      /* 新建空workbook，然后加入worksheet */
      var wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, '授权记录')
      /* 生成xlsx文件 */
      XLSX.writeFile(wb, '授权记录.xlsx')
      this.exportLoading = false
    },
    showStatusText(index, data) {
      return index === 0 ? `有效（${this.licenseStatus[data.status]}）` : '无效'
    },
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      licenseHistory({ pageType: 0 }).then((res) => {
        if (res.code === 0) {
          this.tableData = res.data.list
          this.tableTotal = res.data.list.length
          this.tableLoading = false
        }
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
