<template>
  <span class="score-wrap">
    <slot name="label">
      <span v-if="showLabel">{{ label }}</span>
    </slot>
    <slot v-bind="{ value }" name="value">
      <span v-if="valueShow == '-'">{{ valueShow }}</span>
      <span v-else :class="[highlight ? `is-${Number(value) > 0 ? 'success' : 'error'}` : '']">{{ valueShow }}</span>
      &nbsp;分
    </slot>
  </span>
</template>

<script>
export default {
  name: 'Score',
  components: {
  },
  props: {
    // 分数
    value: {
      type: [Number, String]
    },
    label: {
      type: String,
      default: '得分：'
    },
    // 是否显示分数标题
    showLabel: {
      type: Boolean,
      default: true
    },
    // 是否高亮显示分数
    highlight: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {

    }
  },
  computed: {
    // 最终回显值
    valueShow() {
      return this.value === null || this.value === undefined ? '-' : this.value
    }
  },
  methods: {

  }
}
</script>

<style lang="scss" scoped>
.score-wrap {
  .is-success {
    color: #46ad32;
  }
  .is-error {
    color: #ff2424;
  }
}
</style>
