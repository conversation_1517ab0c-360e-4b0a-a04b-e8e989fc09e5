<template>
  <div class="buttons-wrap">
    <span v-if="options[0] && options[0].className!==null">
      班级：<el-select v-model="value" filterable placeholder="请选择班级" @change="changeValue">
        <el-option
          v-for="(item,index) in options"
          :key="item.className"
          :label="item.className"
          :value="index"/>
      </el-select>
    </span>
    <!-- 仿真共享显示该按钮 -->
    <el-button v-if="isShowRoleConfig" slot="action" :disabled="multipleDisabled" type="primary" @click="clickDrop('roleConfig')">配置角色</el-button>
    <el-button v-if="showRetake" slot="action" :disabled="multipleDisabled" type="primary" @click="clickDrop('retake')">重考</el-button>
    <el-button v-show="selectItem.length == 0" slot="action" type="primary" @click="clickDrop('allStudent')">导出</el-button>
    <el-button v-show="selectItem.length >= 1" slot="action" type="primary" @click="clickDrop('exportExcel')">导出</el-button>
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
          @exportOut="exportDetails('select')"
        />
      </transition>
    </el-dialog>
    <!-- 侧拉弹窗 start -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      append-to-body
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :data="selectItem"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
    <!-- 侧拉弹窗 end -->
  </div>
</template>
<script>
import axios from 'axios'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import retake from './modal-retake.vue'
import exportExcel from './modal-export.vue'
import roleConfig from './roleConfig.vue'
import allStudent from '@/components/modal-export-all.vue'
import { exportExcelFile, timeToFormatTime } from '@/utils'

export default {
  components: {
    retake,
    exportExcel,
    allStudent,
    roleConfig
  },
  mixins: [mixinsActionMenu],
  props: {
    timeParams: {
      type: Object
    },
    paramsData: {
      type: Object
    },
    transmitTime: {
      type: Object
    }
  },
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'retake': '重考',
        'exportExcel': '导出',
        'allStudent': '导出',
        'roleConfig': '配置角色'
      },
      drawerAction: ['roleConfig'], // 需要侧拉打开的操作
      confirmDisabled: false,
      classCode: this.$route.query.classCode,
      schedulingCode: this.$route.query.schedulingCode,
      options: [],
      value: 0,
      topologyAllocation: this.$route.query.topologyAllocation || '',
      curriculumType: this.$route.query.curriculumType || '1'
    }
  },
  computed: {
    isShowRoleConfig() { // 仿真共享模式
      return this.topologyAllocation == '1' && this.curriculumType == '2'
    },
    // 课程类型且为考试模式才显示 “重考” 按钮
    showRetake() {
      return this.$route.query.schedulingType == '课程' && this.$route.query.isExamMode == 1
    }
  },
  inject: ['tableVm'],
  mounted() {
    const resultList = JSON.parse(localStorage.getItem(`manageAffairs_resultList`) || '[]')
    this.options = resultList
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'changeClass') {
        this.$emit('changeClass', data)
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    'clickDrop': function(name) {
      if (this.drawerAction && this.drawerAction.includes(name)) {
        this.drawerName = name
      } else {
        this.modalName = name
      }
    },
    // 选择班级
    changeValue(val) {
      this.confirmCall('changeClass', this.options[val])
    },
    // 导出事务表
    exportDetails(value) {
      if (value === 'all') {
        this.exportAll()
      } else if (value === 'select') {
        this.exportSelect()
      }
    },
    exportAll() {
      const { content } = this.$route.query
      const date = timeToFormatTime(new Date())
      const name = `${content}_学员列表_${date}`
      const query = {
        ...this.timeParams
      }
      axios({
        method: 'post',
        url: '/api/training/PjtSysUser/exportStudentAffairDetails',
        data: query,
        responseType: 'blob'
      }).then((res) => {
        exportExcelFile(res, name)
      })
    },
    exportSelect() {
      const { content } = this.$route.query
      const date = timeToFormatTime(new Date())
      const name = `${content}_学员列表_${date}`
      const query = {
        ...this.timeParams,
        userId: []
      }
      this.selectItem.forEach(item => {
        query.userId.push(item.userId)
      })
      axios({
        method: 'post',
        url: '/api/training/PjtSysUser/exportStudentAffairDetails',
        data: query,
        responseType: 'blob'
      }).then((res) => {
        exportExcelFile(res, name)
      })
    }
  }
}
</script>
