<template>
  <div class="content-wrap-layout wrap">
    <el-breadcrumb style="margin: 15px;" class="detail-breadcrumb" separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ name: 'teacherAffairs' }">{{ '教学事务' }}</el-breadcrumb-item>
      <el-breadcrumb-item>{{ '模拟练习分析' }}</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="detail-content-wrap">
      <el-row :gutter="20">
        <el-col :span="24">
          <detail-card title="基础教学信息">
            <el-form
              slot="content"
              label-position="left"
              label-width="80px"
              label-suffix=":"
            >
              <el-form-item label="名称">{{ typeList.examName }}</el-form-item>
              <el-form-item label="时间">
                {{ sectionTime }}
                {{ sectionSeason.split(",")[0].split("-")[0] }} -
                {{
                  sectionSeason
                    .split(",")
                    [sectionSeason.split(",").length - 1].split("-")[1]
                }}
              </el-form-item>
              <el-form-item label="状态">
                <el-badge :type="status == 1 ? 'info' : (status == 2 ? 'success' : 'danger')" is-dot />{{ status == 1 ? '未开始' : (status == 2 ? '进行中' : '已结束') }}
              </el-form-item>
              <el-form-item :label="`涵盖${selectedTopic == 1 ? '知识点' : '分类'}`">
                <el-tag v-for="(k, i) in typeList.knowledgeVoList" :key="i" style="margin-right: 5px;" class="mb-5">{{ k.knowledgeName }}</el-tag>
              </el-form-item>
              <el-form-item v-if="options[0].className!==null" label="班级">
                <el-select v-model="value" filterable placeholder="请选择班级" @change="changeValue">
                  <el-option
                    v-for="(item,index) in options"
                    :key="item.className"
                    :label="item.className"
                    :value="index"/>
                </el-select>
              </el-form-item>
            </el-form>
          </detail-card>
        </el-col>
      </el-row>
      <div class="main">
        <div class="main-pack">
          <div class="main-pack_title"><span>题目分布</span></div>
          <div id="main-l" ref="mainl" class="main-pack1" />
          <div class="main-pack_foot">
            <div v-for="item in scoredis" :key="item.key">
              <div v-for="keyItme in scoredisIndexesList" :key="keyItme.color">
                <div
                  v-if="keyItme.key == item.key"
                  class="main_pack_foot_content"
                >
                  <div :style="`background: ${keyItme.color};`" class="bar" />
                  <div class="main_pack_foot_content_name">
                    <div class="main_pack_name_color">
                      {{ keyItme.name }}
                    </div>
                    <div class="main_pack_num_color">
                      {{ item.name.substring(0, item.name.indexOf("%")) }}
                      <span class="main_pack_name_color">%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="main-pack">
          <div class="main-pack_title"><span>试题难度</span></div>
          <div id="main-c" ref="mainc" class="main-pack1" />
          <div class="main-pack_foot">
            <div v-for="item in difficulty" :key="item.key">
              <div
                v-for="keyItme in difficultyIndexesList"
                :key="keyItme.color"
              >
                <div
                  v-if="keyItme.key == item.key"
                  class="main_pack_foot_content"
                >
                  <div :style="`background: ${keyItme.color};`" class="bar" />
                  <div class="main_pack_foot_content_name">
                    <div class="main_pack_name_color">
                      {{ keyItme.name }}
                    </div>
                    <div class="main_pack_num_color">
                      {{ item.name.substring(0, item.name.indexOf("%")) }}
                      <span class="main_pack_name_color">%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="main-pack">
          <div class="main-pack_title"><span>成绩分布</span></div>
          <div id="main-r" ref="mainr" class="main-pack1" />
          <div class="main-pack_foot">
            <div v-for="item in distribution" :key="item.key">
              <div
                v-for="keyItme in distributionIndexesList"
                :key="keyItme.color"
              >
                <div
                  v-if="keyItme.key == item.key"
                  class="main_pack_foot_content"
                >
                  <div :style="`background: ${keyItme.color};`" class="bar" />
                  <div class="main_pack_foot_content_name">
                    <div class="main_pack_name_color">
                      {{ keyItme.name }}
                    </div>
                    <div class="main_pack_num_color">
                      {{ item.name.substring(0, item.name.indexOf("%")) }}
                      <span class="main_pack_name_color">%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { examAnalysisApi } from '@/api/teacher/index.js'
import moment from 'moment'
import detailCard from '@/packages/detail-view/detail-card.vue'

export default {
  components: {
    detailCard
  },
  data() {
    return {
      scoreVoList: [],
      typeList: {},
      rateNum: 0,
      scoredis: [],
      difficulty: [],
      distribution: [],
      content: this.$route.query.content,
      sectionTime: '',
      sectionSeason: '',
      selectedTopic: 1,
      examScore: 0,
      scoredisIndexesList: [
        {
          key: 'judgeNumber',
          name: '判断',
          color: '#67C23A'
        },
        {
          key: 'singleNumber',
          name: '单选',
          color: '#FADC19'
        },
        {
          key: 'manyNumber',
          name: '多选',
          color: '#BEDAFF'
        },
        {
          key: 'ctfNumber',
          name: 'CTF',
          color: '#F76560'
        },
        {
          key: 'awdNumber',
          name: 'AWD',
          color: '#94BFFF'
        },
        {
          key: 'loopholeNumber',
          name: '漏洞',
          color: '#4080FF'
        },
        {
          key: 'completionNumber',
          name: '填空',
          color: '#F01256'
        },
        {
          key: 'otherNumber',
          name: '其他',
          color: '#CCCCCC'
        }
      ],
      difficultyIndexesList: [
        {
          key: 'primaryDifficulty',
          name: '初级',
          color: '#FADC19'
        },
        {
          key: 'intermediateDifficulty',
          name: '中级',
          color: '#BEDAFF'
        },
        {
          key: 'seniorDifficulty',
          name: '高级',
          color: '#4080FF'
        }
      ],
      distributionIndexesList: [
        {
          key: 'pass60',
          name: '及格',
          color: '#4080FF'
        },
        {
          key: 'fail60',
          name: '不及格',
          color: '#FADC19'
        },
        {
          key: 'equal100',
          name: '满分',
          color: '#BEDAFF'
        },
        {
          key: 'notSubmit',
          name: '未提交',
          color: '#CCCCCC'
        }
      ],
      options: JSON.parse(this.$route.query.resultList),
      value: 0,
      schedulingCode: this.$route.query.schedulingCode,
      examCode: this.$route.query.examCode,
      classCode: this.$route.query.classCode,
      resultListChange: [JSON.parse(this.$route.query.resultList)[0]]
    }
  },
  computed: {
    // 1 未开始 2 进行中 3 已结束
    status: function() {
      var currentTiem = new Date().getTime()
      var startTiem = new Date(
        this.sectionTime + ' ' + this.sectionSeason.split('-')[0]
      ).getTime()
      var endTiem = new Date(
        this.sectionTime + ' ' + this.sectionSeason.split('-')[1]
      ).getTime()
      if (currentTiem < startTiem) {
        return 1
      }
      if (currentTiem > startTiem && currentTiem < endTiem) {
        return 2
      }
      if (currentTiem > endTiem) {
        return 3
      }
      return 1
    }
  },
  mounted() {
    this.examAnalysis()
  },
  methods: {
    format(percentage) {
      console.log(percentage)
      return `${percentage}分`
    },
    // 选择班级
    changeValue(val) {
      this.schedulingCode = this.options[val].schedulingCode
      this.examCode = this.options[val].curriculumCode
      this.classCode = this.options[val].classCode
      this.resultListChange = [this.options[val]]
      this.examAnalysis()
    },
    // 题目分布
    initAxis() {
      var chartDom = this.$refs.mainl
      var myChart = echarts.init(chartDom)
      myChart.clear()
      // var option
      myChart.setOption({
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            label: {
              show: false,
              position: 'center'
            },
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 20
              }
            },
            labelLine: {
              show: true
            },
            data: this.scoredis
          }
        ]
      }, true)
      window.onresize = function() {
        myChart.resize()
      }
    },
    // 试题难度
    initAxiscon() {
      var chartDom = this.$refs.mainc
      var myChart = echarts.init(chartDom)
      // var option
      myChart.clear()
      myChart.setOption({
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            color: ['#FADC19', '#BEDAFF', '#4080FF'],
            type: 'pie',
            radius: ['50%', '70%'],
            label: {
              show: false,
              position: 'center'
            },
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 20
              }
            },
            labelLine: {
              show: true
            },
            data: this.difficulty
          }
        ]
      }, true)
      window.onresize = function() {
        myChart.resize()
      }
    },
    // 成绩分布
    initAxisrit() {
      var chartDom = this.$refs.mainr
      var myChart = echarts.init(chartDom)
      myChart.clear()
      // var option
      myChart.setOption({
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            label: {
              show: false,
              position: 'center'
            },
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 20
              }
            },
            labelLine: {
              show: true
            },
            data: this.distribution
          }
        ]
      }, true)
      window.onresize = function() {
        myChart.resize()
      }
    },
    // 考试分析
    examAnalysis() {
      examAnalysisApi({
        schedulingCode: this.schedulingCode,
        examCode: this.examCode,
        classCode: this.classCode,
        resultList: this.resultListChange,
        resulStudenttList: JSON.parse(this.$route.query.resultList)
      }).then((res) => {
        this.typeList = res.data[0]
        this.selectedTopic = res.data[0].selectedTopic
        this.examScore = res.data[0].examScore
        this.scoreVoList = res.data[0].examUserScoreVoList
        this.sectionTime = moment(this.typeList.sectionTime).format(
          'YYYY-MM-DD'
        )
        this.sectionSeason = this.typeList.sectionSeason
        // 学员成绩排名
        // 题目分布
        const num =
            this.typeList.judgeNumber +
            this.typeList.singleNumber +
            this.typeList.manyNumber +
            this.typeList.ctfNumber +
            this.typeList.awdNumber +
            this.typeList.loopholeNumber +
            this.typeList.completionNumber +
            this.typeList.otherNumber
        this.scoredis = []
        if (this.typeList.judgeNumber) {
          this.scoredis.push({
            value: this.typeList.judgeNumber,
            name:
                parseFloat(
                  ((this.typeList.judgeNumber / num) * 100).toFixed(2)
                ) + '% ',
            key: 'judgeNumber',
            itemStyle: { color: '#67C23A' }
          })
        } else {
          this.scoredis.push({
            value: 0,
            name: 0 + '%   ',
            key: 'judgeNumber',
            itemStyle: { color: '#67C23A' }
          })
        }
        if (this.typeList.singleNumber) {
          this.scoredis.push({
            value: this.typeList.singleNumber,
            name:
                parseFloat(
                  ((this.typeList.singleNumber / num) * 100).toFixed(2)
                ) + '% ',
            key: 'singleNumber',
            itemStyle: { color: '#FADC19' }
          })
        } else {
          this.scoredis.push({
            value: 0,
            name: 0 + '%   ',
            key: 'singleNumber',
            itemStyle: { color: '#FADC19' }
          })
        }
        if (this.typeList.manyNumber) {
          this.scoredis.push({
            value: this.typeList.manyNumber,
            name:
                parseFloat(((this.typeList.manyNumber / num) * 100).toFixed(2)) +
                '%  ',
            key: 'manyNumber',
            itemStyle: { color: '#BEDAFF' }
          })
        } else {
          this.scoredis.push({
            value: 0,
            name: 0 + '%   ',
            key: 'manyNumber',
            itemStyle: { color: '#BEDAFF' }
          })
        }
        if (this.typeList.ctfNumber) {
          this.scoredis.push({
            value: this.typeList.ctfNumber,
            name:
                parseFloat(
                  ((this.typeList.ctfNumber / num) * 100).toFixed(2)
                ) + '%   ',
            key: 'ctfNumber',
            itemStyle: { color: '#F76560' }
          })
        } else {
          this.scoredis.push({
            value: 0,
            name: 0 + '%   ',
            key: 'ctfNumber',
            itemStyle: { color: '#F76560' }
          })
        }
        if (this.typeList.awdNumber) {
          this.scoredis.push({
            value: this.typeList.awdNumber,
            name:
                '  ' +
                parseFloat(
                  ((this.typeList.awdNumber / num) * 100).toFixed(2)
                ) +
                '%',
            key: 'awdNumber',
            itemStyle: { color: '#94BFFF' }
          })
        } else {
          this.scoredis.push({
            value: 0,
            name: 0 + '%   ',
            key: 'awdNumber',
            itemStyle: { color: '#94BFFF' }
          })
        }
        if (this.typeList.loopholeNumber) {
          this.scoredis.push({
            value: this.typeList.loopholeNumber,
            name:
                ' ' +
                parseFloat(((this.typeList.loopholeNumber / num) * 100).toFixed(2)) +
                '%',
            key: 'loopholeNumber',
            itemStyle: { color: '#4080FF' }
          })
        } else {
          this.scoredis.push({
            value: 0,
            name: 0 + '%   ',
            key: 'loopholeNumber',
            itemStyle: { color: '#4080FF' }
          })
        }
        if (this.typeList.completionNumber) {
          this.scoredis.push({
            value: this.typeList.completionNumber,
            name:
                ' ' +
                parseFloat(((this.typeList.completionNumber / num) * 100).toFixed(2)) +
                '%',
            key: 'completionNumber',
            itemStyle: { color: '#F01256' }
          })
        } else {
          this.scoredis.push({
            value: 0,
            name: 0 + '%   ',
            key: 'completionNumber',
            itemStyle: { color: '#F01256' }
          })
        }
        if (this.typeList.otherNumber) {
          this.scoredis.push({
            value: this.typeList.otherNumber,
            name:
                ' ' +
                parseFloat(((this.typeList.otherNumber / num) * 100).toFixed(2)) +
                '%',
            key: 'otherNumber',
            itemStyle: { color: '#CCCCCC' }
          })
        } else {
          this.scoredis.push({
            value: 0,
            name: 0 + '%   ',
            key: 'otherNumber',
            itemStyle: { color: '#CCCCCC' }
          })
        }
        this.initAxis()
        // 试题难度
        const num2 =
            this.typeList.primaryDifficultyNumber +
            this.typeList.intermediateDifficultyNumber +
            this.typeList.seniorDifficultyNumber
        this.difficulty = []
        if (this.typeList.primaryDifficultyNumber) {
          this.difficulty.push({
            value: this.typeList.primaryDifficultyNumber,
            name:
                '' +
                parseFloat(
                  ((this.typeList.primaryDifficultyNumber / num2) * 100).toFixed(
                    2
                  )
                ) +
                '% ',
            key: 'primaryDifficulty',
            itemStyle: { color: '#FADC19' }
          })
        } else {
          this.difficulty.push({
            value: 0,
            name: 0 + '%   ',
            key: 'primaryDifficulty',
            itemStyle: { color: '#FADC19' }
          })
        }
        if (this.typeList.intermediateDifficultyNumber) {
          this.difficulty.push({
            value: this.typeList.intermediateDifficultyNumber,
            name:
                '' +
                parseFloat(
                  (
                    (this.typeList.intermediateDifficultyNumber / num2) *
                    100
                  ).toFixed(2)
                ) +
                '%  ',
            key: 'intermediateDifficulty',
            itemStyle: { color: '#BEDAFF' }
          })
        } else {
          this.difficulty.push({
            value: 0,
            name: 0 + '%   ',
            key: 'intermediateDifficulty',
            itemStyle: { color: '#BEDAFF' }
          })
        }
        if (this.typeList.seniorDifficultyNumber) {
          this.difficulty.push({
            value: this.typeList.seniorDifficultyNumber,
            name:
                '' +
                parseFloat(
                  ((this.typeList.seniorDifficultyNumber / num2) * 100).toFixed(2)
                ) +
                '%   ',
            key: 'seniorDifficulty',
            itemStyle: { color: '#4080FF' }
          })
        } else {
          this.difficulty.push({
            value: 0,
            name: 0 + '%   ',
            key: 'seniorDifficulty',
            itemStyle: { color: '#4080FF' }
          })
        }
        this.initAxiscon()
        // 成绩分布
        const num4 = this.typeList.pass60 + this.typeList.fail60 + this.typeList.equal100 + (this.typeList.notSubmit || 0)
        this.distribution = []
        if (this.typeList.pass60) {
          this.distribution.push({
            value: this.typeList.pass60,
            name:
                '' +
                parseFloat(((this.typeList.pass60 / num4) * 100).toFixed(2)) +
                '% ',
            key: 'pass60',
            itemStyle: { color: '#4080FF' }
          })
        } else {
          this.distribution.push({
            value: 0,
            name: 0 + '%   ',
            key: 'pass60',
            itemStyle: { color: '#4080FF' }
          })
        }
        if (this.typeList.fail60) {
          this.distribution.push({
            value: this.typeList.fail60,
            name:
                '' +
                parseFloat(((this.typeList.fail60 / num4) * 100).toFixed(2)) +
                '%  ',
            key: 'fail60',
            itemStyle: { color: '#FADC19' }
          })
        } else {
          this.distribution.push({
            value: 0,
            name: 0 + '%   ',
            key: 'fail60',
            itemStyle: { color: '#FADC19' }
          })
        }
        if (this.typeList.equal100) {
          this.distribution.push({
            value: this.typeList.equal100,
            name:
                '' +
                parseFloat(((this.typeList.equal100 / num4) * 100).toFixed(2)) +
                '%   ',
            key: 'equal100',
            itemStyle: { color: '#BEDAFF' }
          })
        } else {
          this.distribution.push({
            value: 0,
            name: 0 + '%   ',
            key: 'equal100',
            itemStyle: { color: '#BEDAFF' }
          })
        }
        if (this.typeList.notSubmit) {
          this.distribution.push({
            value: this.typeList.notSubmit,
            name:
                '' +
                parseFloat(((this.typeList.notSubmit / num4) * 100).toFixed(2)) +
                '%   ',
            key: 'notSubmit',
            itemStyle: { color: '#CCCCCC' }
          })
        } else {
          this.distribution.push({
            value: 0,
            name: 0 + '%   ',
            key: 'notSubmit',
            itemStyle: { color: '#CCCCCC' }
          })
        }
        this.initAxisrit()
      })
    }
  }
}
</script>

  <style lang="scss" scoped>
  .wrap {
    overflow: hidden;
    .detail-content-wrap {
      flex: 1;
      padding: 15px;
      overflow-y: auto;
    }
    .main {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .main-pack {
        flex: 1;
        margin-right: 20px;
        background: #ffffff;
        display: flex;
        border-radius: 4px;
        flex-direction: column;
        .main-pack_title {
          font-size: 16px;
          font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
          font-weight: 500;
          color: #1d2129;
          padding: 20px 0 0 20px;
          border-radius: 4px 4px 0 0;
        }
        .main-pack1 {
          width: 366px;
          height: 316px;
          margin: 0 auto;
        }
        .main-pack_foot {
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #ffffff;
          border-radius: 0 0 4px 4px;
          padding-bottom: 20px;
          .main_pack_foot_content {
            display: flex;
            align-items: center;
            margin: 0 8px;
            .main_pack_foot_content_name {
              display: flex;
              flex-direction: column;
              height: 48px;
              justify-content: space-around;
              .main_pack_name_color {
                white-space: nowrap;
                font-size: 12px;
                font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
                font-weight: 400;
                color: #86909c;
              }
              .main_pack_num_color {
                white-space: nowrap;
                font-size: 12px;
                font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
                font-weight: 500;
                color: #1d2129;
              }
            }
            .bar {
              margin-right: 8px;
              width: 4px;
              height: 48px;
              border-radius: 100px 100px 100px 100px;
              opacity: 1;
            }
          }
        }
      }
      .main-pack:last-child {
        margin-right: 0;
      }
    }
    ::v-deep .el-progress-bar__outer {
      height: 10px !important;
    }
  }

  .task-info_body_left_content {
    display: flex;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    .type {
      display: flex;
      align-items: center;
    }
    .green-color {
      color: #05aa53;
    }
    .blue-color {
      color: #288fef;
    }
    .red-color {
      color: #fa3026;
    }
    .green {
      width: 6px;
      height: 6px;
      background: #05aa53;
      border-radius: 50%;
      margin-right: 3px;
    }
    .blue {
      width: 6px;
      height: 6px;
      background: #288fef;
      border-radius: 50%;
    }
    .red {
      width: 6px;
      height: 6px;
      background: #fa3026;
      border-radius: 50%;
    }
    span {
      color: #999999;
      min-width: 90px;
    }
    .knowledge-div {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .knowledge {
        padding: 10px 15px;
        background: rgba($color: #ffa126, $alpha: 0.1);
        border-radius: 18px;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffa126;
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }
  ::v-deep .el-progress__text {
    font-size: 16px !important;
  }
  ::v-deep {
    .el-progress__text {
      display: none;
    }
    .el-progress {
      width: 95%;
    }
    .el-progress-bar {
      padding: 0;
    }
  }
</style>

