<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      :default-placeholder="'默认搜索姓名'"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="isSingle"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      :selected-data="selectedData"
      current-key="userId"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="columnsObj[item].colMinWidth || colMinWidth"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'sex'">
            {{ scope.row[item] ? (scope.row[item] == 1 ? '男' : '女') : "-" }}
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import { getUserListAPI } from '@/api/usercenter/user'
import module from './config.js'
// 引入封装的方法
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    selectedData: {
      type: Array,
      default: () => []
    },
    // 是否为单选
    isSingle: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      moduleName: module.name,
      searchKeyList: [
        { key: 'realname', label: '姓名', master: true, placeholder: '请输入姓名' },
        { key: 'username', label: '账号', placeholder: '请输入账号' },
        { key: 'sex', label: '性别', placeholder: '请输入性别' },
        { key: 'deptName', label: '企业', placeholder: '请输入企业' },
        { key: 'roleName', label: '角色', placeholder: '请输入角色' }
      ],
      columnsObj: {
        'realname': {
          title: '姓名', master: true, colMinWidth: 110
        },
        'username': {
          title: '账号', colMinWidth: 110
        },
        'sex': {
          title: '性别', colMinWidth: 60
        },
        'roleName': {
          title: '角色'
        },
        'deptName': {
          title: '企业', colMinWidth: 110
        }
      },
      columnsViewArr: [
        'realname',
        'username',
        'sex',
        'roleName',
        'deptName'
      ]
    }
  },
  watch: {
    selectedData: {
      handler(newVal) {
        this.setSelectedData(newVal)
      },
      immediate: true
    }
  },
  methods: {
    getList(showLoading = true) {
      if (showLoading) {
        this.loading = true
      }
      const params = this.getPostData('page', 'limit')
      // 启用状态账号
      params.status = '1'
      // 检测项目负责人、检测主管、检测人员
      params.filterRoles = [181251, 181252, 181253]
      getUserListAPI(params).then((ret) => {
        this.tableData = ret.data ? ret.data.list : []
        this.tableTotal = Number(ret.data.totalRow) || 0
        this.loading = false
        this.handleSelection()
        // 移除初始化选择
        this.setSelectedData(this.selectedData)
      }).catch(() => {
        this.loading = false
      })
    },
    setSelectedData(selectedArr) {
      // 清空所有选中
      setTimeout(() => {
        const tableRef = this.$refs.tableView && this.$refs.tableView.$refs.dataTable
        if (!tableRef) return
        tableRef.clearSelection()
        if (Array.isArray(selectedArr) && selectedArr.length > 0) {
          this.tableData.forEach(row => {
            const found = selectedArr.some(item => item.userId === row.userId)
            if (found) {
              tableRef.toggleRowSelection(row, true)
            }
          })
        }
      }, 500)
    }
  }
}
</script>
