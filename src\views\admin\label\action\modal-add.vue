<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-width="100px" @submit.native.prevent>
      <el-form-item label="上级分类:" prop="parentId">
        <el-cascader
          ref="refHandle"
          v-model="formData.parentId"
          :props="{
            expandTrigger: 'hover',
            checkStrictly: true,
            value: 'id'
          }"
          :options="addTreeList"
          :disabled="editMode"
          @change="changeParentId"
        />
      </el-form-item>
      <el-form-item label="标签名称" prop="lableName">
        <el-input v-model.trim="formData.lableName"/>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import validate from '@/packages/validate'
import { addLabel, updateLabel, getLabel, listLabelTree } from '@/api/admin/label'

export default {
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    },
    labelId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      addTreeList: [],
      formData: {
        'parentId': '',
        'lableName': ''
      },
      loading: false,
      validate: validate,
      rules: {
        'parentId': [
          validate.required('change')
        ],
        'lableName': [
          validate.required('blur')
        ]
      }
    }
  },
  computed: {
    editMode() {
      return this.name === 'modalModify'
    },
    activeItem() {
      return this.data[0]
    }
  },
  created() {
    this.getTreeList()
    if (this.editMode && this.activeItem) {
      this.getLabelDetail()
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    getTreeList() {
      listLabelTree({}).then(res => {
        if (res.data) {
          this.addTreeList = res.data
          for (const ntem of this.allTreeList) {
            if ([105, 106, 107, 108, 109].includes(ntem.id) && ntem.children && ntem.children.length) {
              for (const item of ntem.children) {
                if (item) {
                  item.disabled = true
                }
              }
            }
          }
          this.changeTree(this.addTreeList, 0)
        }
      })
    },
    changeTree(tree, deep) {
      tree.forEach(e => {
        e.value = e.id
        if (deep >= 4) {
          e.disabled = true
        }
        if (e.children != undefined) {
          this.changeTree(e.children, deep + 1)
        }
      })
    },
    getLabelDetail() {
      getLabel(this.activeItem.lableId).then(res => {
        if (res.data) {
          this.formData = {
            ...res.data
          }
        }
      })
    },
    changeParentId(val) {
      // 处理父级ID变化
    },
    confirm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const postData = JSON.parse(JSON.stringify(this.formData))
          // 处理祖先节点
          postData.ancestors = ''
          if (Array.isArray(postData.parentId)) {
            postData.parentId.forEach(e => {
              postData.ancestors = postData.ancestors + e + ','
            })
            postData.ancestors = postData.ancestors.substring(0, postData.ancestors.length - 1)
            postData.lableNum = postData.parentId.length
            postData.parentId = postData.parentId.pop()
          } else {
            postData.lableNum = 2
          }
          if (this.editMode) {
            // 编辑模式
            updateLabel({
              lableId: this.activeItem.lableId,
              lableName: postData.lableName
            }).then(res => {
              if (res.code === 200 || res.code === 0) {
                this.$message.success('修改成功')
                this.$emit('call', 'refresh')
                this.close()
              }
            }).catch(() => {
              this.loading = false
            })
          } else {
            // 添加模式
            addLabel(postData).then(res => {
              if (res.code === 200 || res.code === 0) {
                this.$message.success('新增成功')
                this.$emit('call', 'refresh')
                this.close()
              }
            }).catch(() => {
              this.loading = false
            })
          }
        }
      })
    }
  }
}
</script>
