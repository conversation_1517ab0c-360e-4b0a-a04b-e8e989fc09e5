<template>
  <div class="flex-col h-100">
    <div class="title">
      <span class="span-two" @click="goAffairs">教学事务</span>
      / <span class="span-three">{{ content }}</span>
    </div>
    <div class="main flex-1 flex-col h-0">
      <div class="main-title">
        <div class="screen-div">
          <div class="class-div">
            <span>上课状态：</span>
            <el-select v-model="courseState" clearable @change="affairDetails()">
              <el-option key="whole" label="全部" value=""/>
              <el-option key="0" label="未签到" value="0"/>
              <el-option key="1" label="已签到" value="1"/>
            </el-select>
          </div>
          <div class="class-type">
            <span>学习状态：</span>
            <el-select v-model="studyState" clearable @change="affairDetails()">
              <el-option key="whole" label="全部" value=""/>
              <el-option key="0" label="未完成" value="0"/>
              <el-option key="1" label="已完成" value="1"/>
              <el-option key="2" label="进行中" value="2"/>
            </el-select>
          </div>
          <div>
            <el-input
              v-model.trim="likeName"
              style="width:280px;"
              placeholder="搜索学生"
              suffix-icon="el-icon-search"
              clearable
              @change="likeNameFn()"/>
          </div>
        </div>
        <div class="btn" @click="searchMajorStudentExport">
          导出
        </div>
      </div>
      <div class="flex-1 flex-col h-0">
        <el-table
          :data="affairDetailsList"
          height="auto"
          style="width: 100%">
          <el-table-column
            prop="userName"
            label="学生姓名"/>
          <el-table-column
            prop="reviewState"
            label="批阅状态"
            show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.reviewState == 0" style="color:#FFA126;">未批阅</span>
              <span v-else>已批阅</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="courseState"
            label="上课状态">
            <template slot-scope="scope">
              <span v-if="scope.row.courseState == 0">未签到</span>
              <span v-else>已签到</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="studyState"
            label="学习状态">
            <template slot-scope="scope">
              <span v-if="scope.row.studyState == 0">未完成</span>
              <span v-else-if="scope.row.studyState == 1">已完成</span>
              <span v-else>正在进行</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="address"
            label="得分情况">
            <template slot-scope="scope">
              {{ scope.row.score || 0 }}
            </template>
          </el-table-column>
          <el-table-column
            prop="address"
            label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="paging-div">
          <el-pagination
            v-if="affairDetailsList.length != 0"
            :current-page="pageNum"
            :page-sizes="[10, 20, 30, 40, 50, 100, 200]"
            :page-size="pageSize"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import {
  affairDetailsApi
} from '@/api/teacher/index.js'
export default {
  data() {
    return {
      value1: '',
      pageSize: 10,
      pageNum: 1,
      total: 0,
      schedulingCode: this.$route.query.schedulingCode,
      classCode: this.$route.query.classCode,
      affairDetailsList: [],
      courseState: '',
      likeName: '',
      studyState: '',
      content: this.$route.query.content
    }
  },
  created() {
    this.affairDetails()
  },
  methods: {
    async searchMajorStudentExport() {
      await axios({
        method: 'post',
        url: '/api/training/PjtSysUser/exportAffairDetails',
        data: {
          state: 3,
          courseState: this.courseState,
          studyState: this.studyState,
          likeName: this.likeName,
          schedulingCode: this.schedulingCode,
          classCode: this.classCode
        },
        responseType: 'blob'
      }).then((res) => {
        const url = window.URL.createObjectURL(new Blob([res.data]))
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', `教学事务-考试列表.xls`)
        document.body.appendChild(link)
        link.click()
      })
    },
    goPage() {
      // this.$router.push({
      //   path: '/teacher'
      // })
    },
    goAffairs() {
      this.$router.push({
        path: '/manage/training/affairs'
      })
    },
    handleClick(item) {
      this.$router.push({
        path: 'consultExamination',
        query: {
          classCode: this.classCode,
          evaluationCode: item.evaluationCode,
          practiceEvaluationCode: item.practiceEvaluationCode,
          userName: item.userName,
          sectionTime: this.$route.query.sectionTime,
          sectionSeason: this.$route.query.sectionSeason,
          content: this.$route.query.content,
          curriculumCode: this.$route.query.curriculumCode
        }
      })
    },
    likeNameFn() {
      this.pageNum = 1
      this.affairDetails()
    },
    affairDetails() {
      const data = {
        state: 3,
        courseState: this.courseState,
        studyState: this.studyState,
        likeName: this.likeName,
        classCode: this.classCode,
        schedulingCode: this.schedulingCode,
        limit: this.pageSize,
        page: this.pageNum
      }
      affairDetailsApi(data).then(res => {
        this.affairDetailsList = res.data.records
        this.total = res.data.total
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.pageNum = 1
      this.affairDetails()
    },
    handleCurrentChange(val) {
      this.pageNum = val
      this.affairDetails()
    }
  }
}
</script>

<style lang="scss" scoped>
.title {
  font-size: 14px;
  padding-bottom: 15px;
  color: #999999;
  .span-one{
    margin-right: 10px;
    cursor: pointer;
    font-family: Microsoft YaHei;
    font-weight: 400;
  }
  .span-one:hover{
    margin-right: 10px;
    color: #333333;
    cursor: pointer;
    font-family: Microsoft YaHei;
    font-weight: 400;
  }
  .span-two{
    margin-right: 10px;
    cursor: pointer;
    font-family: Microsoft YaHei;
    font-weight: 400;
  }
  .span-two:hover{
    color: #333333;
    cursor: pointer;
    font-family: Microsoft YaHei;
    font-weight: 400;
  }
  .span-three{
    margin: 0 10px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #333333;
  }
}
.main{
  border-radius: 2px;
  padding: 15px;
  background: #FFFFFF;
  box-sizing: border-box;
  .main-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 25px;
    .screen-div{
      display: flex;
      align-items: center;
      .date-div{
        margin-right: 30px;
        span{
          margin-right: 8px;
          color: #666666;
          font-size: 16px;
        }
        ::v-deep{
          .el-date-editor--daterange.el-input__inner{
            width: 260px;
          }
          .el-date-editor .el-range__icon{
            line-height: normal;
            justify-content: center;
            padding: 3px 0 0 0 ;
            display: flex;
            align-items: center;
          }
          .el-date-editor .el-range-input{
            line-height: normal;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .el-range-separator{
            line-height: normal;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .el-date-editor .el-range__close-icon{
            line-height: normal;
            padding: 3px 0 0 0 ;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }
      .class-div{
        margin-right: 30px;
        span{
          margin-right: 8px;
          color: #666666;
          font-size: 16px;
        }
      }
      .class-type{
        margin-right: 30px;
        span{
          margin-right: 8px;
          color: #666666;
          font-size: 16px;
        }
      }
    }
    .btn{
      height: 32px;
      font-size: 14px;
      color: #FFFFFF;
      padding: 0px 16px;
      background: #288FEF;
      display: flex;
      align-items: center;
      border-radius: 2px;
      cursor: pointer;
    }
    .btn:hover{
      border-radius: 2px;
      cursor: pointer;
      display: flex;
      align-items: center;
      height: 32px;
      font-size: 14px;
      color: #FFFFFF;
      padding: 0px 16px;
      background: #288FEF;
      opacity: 0.4;
    }
    .btn:active{
      border-radius: 2px;
      cursor: pointer;
      display: flex;
      align-items: center;
      height: 32px;
      font-size: 14px;
      color: #FFFFFF;
      padding: 0px 16px;
      background: #0790E9;
      opacity: 1;
    }
  }
  ::v-deep {
        .el-table th > .cell{
          color: #212325;
          font-size: 14px;
          padding: 5px 0 5px 66px;
        }
        .el-table td.el-table__cell div{
          color: #595959;
          padding-left: 70px;
          font-size: 14px;
        }
        /* 表格内背景颜色 */
        .el-table th {
          background: #E3EFFC;
        }
        .el-table__header-wrapper {
          border: 2px solid #95C6F5;
        }
        /* 清除底部横线 */
        .el-table::before {
          height: 0px;
        }
        .el-table__body-wrapper::-webkit-scrollbar {
          height: 13px;
        }
        .el-table {
          tbody .el-table__row:hover {
            background: #F8F9FA;
            box-shadow: 0px 2px 4px 0px #D9E2EC;
        }
      }
  }
  .paging-div{
    margin-top: 20px;
    display: flex;
    justify-content: right;
  }
}
</style>
