<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div v-if="name == 'open'" slot="title">
        <p>使用权限公开后教案中课程体系所有教师可见，体系中若包含"私有"课程，只展示课程名称，不能查看详情。</p >
      </div>
      <div v-else slot="title">
        <p>使用权限私有后教案仅对自己和管理员可见。</p >
      </div>
    </el-alert>
    <batch-template
      :data="data"
      :available-data="availableData"
      view-key="name"
    />
    <el-checkbox v-model="checked">我已知晓上述风险</el-checkbox>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!isChecked" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import { lessonPlanUpdate } from '@/api/teacher/index.js'
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
export default {
  name: 'ModalPublic',
  components: {
    batchTemplate
  },
  mixins: [],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: false,
      checked: false,
      availableData: []
    }
  },
  computed: {
    isChecked() {
      if (this.availableData.length == 0) {
        return false
      } else if (this.availableData.length > 0 && this.checked == true) {
        return true
      }
    }
  },
  mounted() {
    this.filterData()
  },
  methods: {
    filterData() {
      if (this.name === 'open') {
        this.availableData = this.data.filter(item => {
          return item.privacy === 0
        })
      } else {
        this.availableData = this.data.filter(item => {
          return item.privacy === 1
        })
      }
    },
    saveJoinAuth(postData) {
      return new Promise((resolve, reject) => {
        lessonPlanUpdate(postData).then(res => {
          resolve(res)
        })
      })
    },
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      const idArr = this.availableData.map(item => {
        return { name: item.name, id: item.id, privacy: item.privacy }
      })
      idArr.map((item, index) => {
        this.saveJoinAuth({ name: item.name, id: item.id, privacy: item.privacy == 1 ? 0 : 1, source: 'back' })
          .then((res) => {
            this.$message.success(res.data)
            this.$emit('call', 'refresh')
            this.close()
          })
      })
    }
  }
}
</script>
