<template>
  <div class="drawer-wrap">
    <selected-role
      ref="table"
      :filter-data="{}"
      :height="null"
      :link="false"
      :single="false"
      @on-select="onSelect"
    />
    <div class="drawer-footer">
      <div>
        <el-button :disabled="!selectedItem.length" type="primary" @click="confirm">确定</el-button>
        <el-button type="text" @click="close">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import selectedRole from '../../selectedRole/index.vue'
import { studentAllocateRole } from '@/api/topo.js'
export default {
  components: {
    selectedRole
  },
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectedItem: [],
      notAllowedArr: [],
      sceneInstanceId: this.$route.query.sceneInstanceId || ''
    }
  },
  computed: {
    userIdArr() {
      if (this.data && this.data.length > 0) {
        return this.data.map(item => item.userId)
      } else {
        return []
      }
    }
  },
  methods: {
    'onSelect': function(data) {
      this.selectedItem = data
    },
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      const params = {
        sceneInstanceId: this.sceneInstanceId,
        initRoleId: this.selectedItem[0].initRoleId,
        studentIdList: this.userIdArr,
        roleId: this.selectedItem[0].roleId,
        schedulingCode: this.$route.query.schedulingCode
      }
      studentAllocateRole(params).then(res => {
        this.$message.success('角色分配成功。')
        this.$emit('call', 'refresh')
        this.$emit('close')
      })
    }
  }
}
</script>
<style lang="scss">
.drawer-footer{
  display: flex;
  align-items: center;
  height: 8%;
}
</style>
