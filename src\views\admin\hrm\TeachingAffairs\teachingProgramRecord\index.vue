<template>
  <div class="content-wrap-layout">
    <contentHeader :title="title" :des="des"/>
    <category :category-name="moduleName" :status="status" @category-query="categoryQuery" />
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      :filter-data="{ 'status': status }"
      :cache-pattern="true"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
    >
      <action-menu
        slot="action"
        :module-name="moduleName"
        :select-item="selectItem"
        @call="actionHandler"
      />
    </page-table>
    <el-drawer
      :visible.sync="detailShow"
      :modal="false"
      size="75%"
      append-to-body
      class="detail-view"
      @close="closeDetail"
    >
      <t-detail v-if="detailShow" />
    </el-drawer>
  </div>
</template>

<script>
import moduleConf from './config'
import pageTable from './table/index.vue'
import category from './category/index.vue'
import actionMenu from './action/index.vue'
import contentHeader from '../content-header.vue'
import lodash from 'lodash'
import tDetail from './detail/index.vue'
import moduleMixin from '@/packages/mixins/module_list'
export default {
  name: moduleConf.name,
  components: {
    contentHeader,
    pageTable,
    actionMenu,
    category,
    tDetail
  },
  mixins: [moduleMixin],
  data() {
    return {
      listRouterName: 'teachingProgramRecord',
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      title: '教学事务',
      num: 0,
      des: '对教学事务一应数据展示分析模块',
      status: ''
    }
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, query: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {},
    categoryQuery: lodash.debounce(function(obj) {
      this.status = obj.value
      if (!this.$store.state.cache[this.moduleName]) {
        const obj = {
          data: { searchShow: true },
          key: this.moduleName
        }
        this.$store.commit('SET_CACHE', obj)
      }
      this.$nextTick(() => {
        if (this.num != 0) {
          this.$refs['table']['pageCurrent'] = 1
        } else {
          this.num = this.num + 1
        }
        this.$refs['table'].getList(false)
      })
    }, 500)
  }
}
</script>
