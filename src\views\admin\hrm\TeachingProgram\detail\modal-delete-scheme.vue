<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">删除体系会连带体系中的课程一并移除，请确认是否删除?</div>
    </el-alert>
    <batch-template
      :data="data"
      :available-data="data"
      :show-delete-warning="false"
      :view-key="data.name"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!data.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { lessonPlanHierarchyDelete, lessonPlanDetailQuery } from '@/api/teacher/index.js'
export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      treeData: [],
      addItemId: ''
    }
  },
  created() {
    this.detailQuery()
  },
  methods: {
    // 获取方案详情
    detailQuery() {
      const params = {
        lessonPlanId: this.$route.query.id
      }
      lessonPlanDetailQuery(params).then(res => {
        this.treeData = [res.data]
        this.treeData.forEach(item => {
          item.type = 0
          if (item.children) {
            item.children.forEach(it => {
              it.type = 1
              if (it.children) {
                it.children.forEach(i => {
                  i.type = 2
                })
              }
            })
          }
        })
        this.filterAddItem()
      })
    },
    // 查找新增体系节点
    filterAddItem() {
      const addItem = this.treeData[0].children.filter(item => {
        return item.name === this.data[0].name
      })
      this.addItemId = addItem[0].id
    },
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const params = {
        lessonPlanId: this.$route.query.id,
        hierarchyId: this.addItemId
      }
      lessonPlanHierarchyDelete(params).then((res) => {
        if (res.code === 0) {
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          // 实时更新删除节点
          const nodeId = this.$parent.$parent.$refs.treeRef.getCurrentKey()
          const node = this.$parent.$parent.$refs.treeRef.getNode(nodeId)
          const parent = node.parent
          const children = parent.data.children || parent.data
          const index = children.findIndex(d => d.id === node.data.id)
          children.splice(index, 1)
          this.$emit('call', 'refresh')
          this.close()
        }
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>
