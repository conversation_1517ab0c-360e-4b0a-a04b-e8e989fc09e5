<template>
  <div class="content-wrap-layout">
    <el-breadcrumb style="margin: 15px;" class="detail-breadcrumb" separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ name: 'teacherAffairs' }">{{ '教学事务' }}</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ name: 'affairsSimulation',query: $route.query }">{{ content }}</el-breadcrumb-item>
      <el-breadcrumb-item>{{ affairData.realname }}</el-breadcrumb-item>
    </el-breadcrumb>
    <div v-loading="loading" class="detail-content-wrap">
      <el-row :gutter="20">
        <el-col :span="6">
          <detail-card title="基础教学信息">
            <el-form
              slot="content"
              label-position="left"
              label-width="80px"
              label-suffix=":"
            >
              <el-form-item label="教学名称">
                <div class="contentType">
                  {{ content }}&nbsp;<el-tag v-if="contentType" size="mini" class="sanpack">{{ contentType }}</el-tag>
                </div>
              </el-form-item>
              <el-form-item label="教学时间">
                <div>
                  <span>{{ sectionTime.split(" ")[0] }}&nbsp;</span>
                  <span >{{ sectionSeason.slice(0, 6) }}{{ sectionSeason.substr(-5) }}</span>
                </div>
                <div>{{ getweekday(sectionTime.split(" ")[0]) }}</div>
              </el-form-item>
            </el-form>
          </detail-card>
          <detail-card title="答题信息" class="mt-10">
            <el-form
              slot="content"
              label-position="left"
              label-width="110px"
              label-suffix=":"
            >
              <el-form-item label="答题时长">{{ answerTimeHms }}</el-form-item>
              <el-form-item label="总题数">{{ total }}</el-form-item>
              <el-form-item label="题目总分数">{{ totalScore }}</el-form-item>
              <el-form-item label="总成绩">{{ studentScore }}</el-form-item>
            </el-form>
          </detail-card>
          <detail-card v-if="schedulingType == '模拟练习' || affairData.isExamMode == 1" :title="affairData.realname" class="mt-10">
            <el-form
              slot="content"
              label-position="left"
              label-width="110px"
              label-suffix=":"
            >
              <el-form-item label="课程分数">{{
                affairData.examScore
              }}</el-form-item>
              <el-form-item :label="`${courseType}排名`">
                {{ affairData.ranking }}
              </el-form-item>
              <el-form-item :label="`${courseType}平均分`">
                {{ affairData.avgExamScore }}
              </el-form-item>
              <div class="btn-wrap">
                <el-button
                  type="primary" @click="previousPerson"
                >上一人</el-button
                >
                <el-button type="primary" @click="nextPerson">下一人</el-button>
              </div>
            </el-form>
          </detail-card>
        </el-col>
        <el-col :span="18" class="detail-content-right">
          <detail-card title="题目信息">
            <QuestionBank
              slot="content"
              :page-type="schedulingType == '模拟练习' ? 'testPaper' : 'simulation'"
              :question-list="examResultsDetailsVos"
              :answer-sheet="true"
              :show-answer-info="false"
              :answer-time="answerTime"
              @queryPracticeAffair="queryPracticeAffair"
            />
          </detail-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import {
  searchExperimentReportApi,
  queryPracticeAffairAPI,
  queryExamInfoAPI
} from '@/api/teacher/index.js'
import { affairDetailsOrderScore } from '@/api/teachingAffairs/index.js'
import detailCard from '@/packages/detail-view/detail-card.vue'
import QuestionBank from '@/components/QuestionBank/QuestionDetail'
import { getweekday } from '@/utils'
import { contentTypeObj } from '@/views/admin/hrm/personnelManagement/student/constants.js'

export default {
  components: {
    detailCard,
    QuestionBank
  },
  data() {
    return {
      loading: false,
      schedulingCode: this.$route.query.schedulingCode,
      userId: this.$route.query.userId,
      resultList: [],
      consultList: {},
      img: require('@/assets/empty_state.png'),
      evaluationCode: this.$route.query.evaluationCode,
      practiceEvaluationCode: this.$route.query.practiceEvaluationCode,
      classCode: this.$route.query.classCode,
      answerTime: +this.$route.query.answerTime,
      upAndDown: 0,
      affairData: [],
      studentData: [],
      curriculumCode: this.$route.query.curriculumCode,
      schedulingType: this.$route.query.schedulingType,
      sectionTime: this.$route.query.sectionTime,
      sectionSeason: this.$route.query.sectionSeason,
      content: this.$route.query.content,
      total: 0, // 总题数
      totalScore: 0, // 总分数
      studentScore: 0, // 总成绩
      isClass: this.$route.query.classCode.length > 15, // 是否是班级
      examResultsDetailsVos: []
    }
  },
  computed: {
    // 课程内容类型
    contentType() {
      return contentTypeObj[this.$route.query.curriculumType]
    },
    courseType() {
      return this.isClass ? '班级' : '学员'
    },
    // 答题时间
    answerTimeHms() {
      // 模拟练习类型
      if (this.schedulingType === '模拟练习') {
        return this.calculateMinutes(this.sectionSeason)
      }
      const answerTime = this.answerTime
      if (answerTime === 0) return '不限时'
      return (answerTime / 60).toFixed(0) + '分钟'
    }
  },
  created() {
    this.getStudentList()
    this.queryPracticeAffair()
  },
  methods: {
    getweekday: getweekday,
    // 获取考生列表
    getStudentList() {
      const resultList = JSON.parse(localStorage.getItem(`manageAffairs_resultList`) || '[]')
      this.resultList = resultList
      const state = this.$route.query.schedulingType == '模拟练习' ? 3 : 2
      const data = {
        pageNum: 1,
        pageSize: 1000,
        state: state,
        courseState: '',
        studyState: '',
        likeName: '',
        classCode: this.classCode,
        schedulingCode: this.schedulingCode,
        resultList: this.resultList
      }
      affairDetailsOrderScore(data).then(res => {
        if (res.code === 0) {
          this.studentData = res.data || []
        }
      })
    },
    queryPracticeAffair(type) {
      this.loading = true
      let userIdList = []
      const resultList = this.resultList
      if (resultList) {
        userIdList = resultList
      }
      const data = {
        isDynamic: this.$route.query.isDynamic,
        practiceEvaluationCode: this.practiceEvaluationCode,
        type: '1',
        evaluationCode: this.evaluationCode,
        classCode: this.classCode,
        userId: this.userId,
        upAndDown: this.upAndDown,
        userIdList: userIdList,
        schedulingCode: this.schedulingCode,
        examCode: this.curriculumCode
      }
      const requestApi =
        this.schedulingType == '模拟练习'
          ? queryExamInfoAPI
          : queryPracticeAffairAPI
      requestApi(data)
        .then((res) => {
          // 手动评分更新学生得分
          if (type === 'refresh') {
            this.studentScore = 0
            res.data.examResultsDetailsVos.forEach((item) => {
              this.studentScore += Number(item.questionStudentScore)
            })
            return
          }
          this.affairData = res.data
          this.userId = res.data.userId
          this.examResultsDetailsVos = res.data.examResultsDetailsVos || []
          this.searchExperimentReport()
          this.total = res.data.examResultsDetailsVos.length
          this.totalScore = 0
          this.studentScore = 0
          this.examResultsDetailsVos.forEach((item) => {
            this.totalScore += Number(item.questionScore)
            this.studentScore += Number(item.questionStudentScore)
          })
        })
        .finally(() => {
          this.loading = false
        })
    },
    searchExperimentReport() {
      const data = {
        schedulingCode: this.schedulingCode,
        userId: this.userId
      }
      searchExperimentReportApi(data).then((res) => {
        this.consultList = res.data || {}
      })
    },
    // 获取上一个或下一个学员数据
    getStudentItem(n) {
      let tempObj = {}
      const currentIdx = this.studentData.findIndex(item => item.userId == this.userId)
      console.log('currentIdx:', currentIdx)
      if (n == 1 && currentIdx > 0) {
        tempObj = this.studentData[currentIdx - 1]
      } else if (n == 2 && currentIdx < this.studentData.length - 1) {
        tempObj = this.studentData[currentIdx + 1]
      }
      this.evaluationCode = tempObj.evaluationCode
    },
    // 上一个
    previousPerson() {
      this.upAndDown = 1
      this.getStudentItem(1)
      this.queryPracticeAffair()
    },
    // 下一个
    nextPerson() {
      this.upAndDown = 2
      this.getStudentItem(2)
      this.queryPracticeAffair()
    },
    // 计算两个时间段内的分钟数
    calculateMinutes(timeRangeStr) {
      const [startTimeStr, endTimeStr] = timeRangeStr.split('-')
      const [startHours, startMinutes] = startTimeStr.split(':').map(Number)
      const [endHours, endMinutes] = endTimeStr.split(':').map(Number)
      const startMinutesTotal = startHours * 60 + startMinutes
      const endMinutesTotal = endHours * 60 + endMinutes
      const diffMinutes = endMinutesTotal - startMinutesTotal
      const roundedMinutes = Math.round(diffMinutes)
      return `${roundedMinutes}分钟`
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-content-wrap {
  flex: 1;
  min-height: 0;
  padding: 15px;
  /deep/ .el-breadcrumb {
    &.detail-breadcrumb {
      padding: 12px 24px;
      font-size: 12px;
      line-height: 1.5715;
    }
  }
  .el-row {
    width: 100%;
    height: 100%;
  }
  .detail-content-right {
    height: 100%;
    ::v-deep .detail-card {
      height: 100%;
      display: flex;
      flex-direction: column;
      .detail-card-body {
        flex: 1;
        min-height: 0;
        padding: 0;
      }
    }
    ::v-deep .question_wrap {
      border: none;
    }
  }
  .content-right {
    height: 100%;
  }
  .contentType {
    .sanpack {
      background: #f2f3f5;
      color: #4e5969;
      border-radius: 4px;
      margin-top: 2px;
      padding: 0 8px !important;
    }
  }
}

.btn-wrap {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  ::v-deep {
    .el-button {
      width: 121px;
      height: 32px;
      background: #f2f3f5;
      border-radius: 4px;
      border: none;
      font-size: 14px;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      font-weight: 400;
      color: #4e5969;
    }
    .el-button:hover {
      background: #c9cdd4;
    }
  }
}
</style>
