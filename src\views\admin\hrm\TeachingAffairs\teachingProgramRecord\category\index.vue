<template>
  <div class="category-wrap">
    <transverse-list
      :data="teachingState"
      :allow-deletion="false"
      :allow-add="false"
      :all="true"
      :module-name="categoryName + '_' + moduleName + '_type'"
      :cache-pattern="true"
      :is-show-expand="false"
      v-bind="categoryProps"
      title="排课状态"
      @node-click="handleNodeClick($event, 'status')"
    />
  </div>
</template>
<script>
import transverseList from '@/packages/transverse-list/index.vue'
import module from '../config.js'

export default {
  components: {
    transverseList
  },
  props: {
    status: [String, Number],
    examFinish: [String, Number],
    publicStatus: [String, Number],
    categoryName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      moduleName: module.name,
      teachingState: [{ label: '未完成', value: 1 }, { label: '已完成', value: 0 }],
      categoryProps: {
        label: 'label',
        idName: 'value'
      }
    }
  },
  mounted() {
  },
  methods: {
    handleNodeClick(item, key) {
      this.$emit('category-query', item)
    }
  }
}
</script>
