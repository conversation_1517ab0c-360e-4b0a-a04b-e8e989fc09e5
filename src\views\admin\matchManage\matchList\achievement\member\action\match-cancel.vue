<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        取消个人成绩会将该成员当前获得的成绩全部清零，并且会将该成员当前在战队中所有贡献的成绩同步去除。成绩清零或去除后无法找回，请谨慎操作。</div>
    </el-alert>
    <batch-template
      :data="data"
      :available-data="data"
      :show-delete-warning="false"
      post-key="status"
      view-key="realName"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import batchTemplate from '@/packages/batch-delete/modal-bat-template'
import modalMixins from '@/packages/mixins/modal_form'
import { cancelSeasonPlayerScore } from '@/api/match/index'

export default {
  name: 'Cancel',
  components: { batchTemplate },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: false,
      checked: false
    }
  },
  computed: {
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const result = this.data.map(item => {
        return item.playerId
      })
      const params = {
        bigMatchSeasonId: this.$route.params.id,
        playerIdList: result
      }
      // 批量调用接口
      cancelSeasonPlayerScore(params).then(res => {
        this.$message.success('成绩取消成功')
        this.$emit('call', 'refresh')
        this.close()
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>
