export default {
  name: 'matchMemberAchievement',
  columnsArr: [
    // 个人赛
    {
      'ranking': {
        title: '排名', master: true
      },
      'realName': {
        title: '姓名'
      },
      'userName': {
        title: '账号'
      },
      'teamName': {
        title: '所属战队'
      },
      'score': {
        title: '成绩'
      }
    },
    {
      // 团队赛
      'ranking': {
        title: '排名', master: true
      },
      'teamName': {
        title: '战队名称'
      },
      'leaderName': {
        title: '指挥官'
      },
      'affiliatedUnit': {
        title: '所属单位'
      },
      'score': {
        title: '成绩'
      }
    }
  ],
  // 当前显示列key表 默认，如果localStorage有数据将被覆盖
  columnsViewScoreArr: [
    [
      'ranking',
      'realName',
      'userName',
      'teamName',
      'score'
    ],
    [
      'ranking',
      'teamName',
      'leaderName',
      'affiliatedUnit',
      'score'
    ]
  ],
  searchKeyArr: [
    [
      { key: 'realName', label: '姓名', master: true, placeholder: '默认搜索姓名' },
      { key: 'teamName', label: '所属战队' },
      { key: 'scoreEnd', label: '成绩<=' },
      { key: 'scoreStart', label: '成绩>=' }
    ],
    [
      { key: 'teamName', label: '战队名称', master: true, placeholder: '默认搜索战队名称' },
      { key: 'affiliatedUnit', label: '所属单位' },
      { key: 'leaderName', label: '指挥官' },
      { key: 'scoreEnd', label: '成绩<=' },
      { key: 'scoreStart', label: '成绩>=' }
    ]
  ]
}
