<template>
  <div class="category-wrap">
    <transverse-list
      :data="statusArr"
      :allow-deletion="false"
      :allow-add="false"
      :all="true"
      v-bind="categoryProps"
      title="排课情况"
      @node-click="handleNodeClick($event, 'status')"
    />
  </div>
</template>
<script>
import transverseList from '@/packages/transverse-list/index.vue'
import module from '../config.js'

export default {
  components: {
    transverseList
  },
  props: {
    status: [String, Number]
  },
  data() {
    return {
      moduleName: module.name,
      statusArr: module.status,
      categoryProps: {
        label: 'label',
        idName: 'value'
      }
    }
  },
  mounted() {
  },
  methods: {
    handleNodeClick(item, key) {
      this.status = item.value
      this.$emit('category-query', this.status)
    }
  }
}
</script>
