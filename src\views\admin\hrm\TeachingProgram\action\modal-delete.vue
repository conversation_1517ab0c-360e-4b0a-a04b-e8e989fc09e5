<template>
  <div v-loading="loading" class="dialog-wrap">
    <batch-template
      :data="data"
      :available-data="availableData"
      :show-delete-warning="false"
      view-key="name"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="checked" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { lessonPlanDelete } from '@/api/teacher/index.js'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false
    }
  },
  computed: {
    availableData() {
      return this.data.filter(item => item.inScheduling === false)
    },
    checked() {
      return this.availableData.length === 0
    }
  },
  mounted() {
    this.filterData()
  },
  methods: {
    filterData() {
      this.availableData = this.data.filter(item => {
        return item.inScheduling === false
      })
      this.availableData.length == 0 ? this.checked = true : this.checked = false
    },
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const that = this
      const result = this.availableData.map(item => {
        const params = { id: item.id, source: 'back' }
        return lessonPlanDelete(params)
      })
      Promise.all(result).then((res, error) => {
        if (result.length === res.length) {
          that.close()
          that.$emit('call', 'refresh')
          that.loading = false
          that.$message.success('删除成功')
        }
      }).catch(() => {
        that.close()
        that.loading = false
      })
    }
  }
}
</script>
