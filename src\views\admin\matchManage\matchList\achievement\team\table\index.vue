<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        阵营：
        <el-radio-group v-model="camp" size="small" @input="getList()">
          <el-radio-button label="" >全部</el-radio-button>
          <el-radio-button label="1">红方</el-radio-button>
          <el-radio-button label="2">蓝方</el-radio-button>
        </el-radio-group>
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索战队名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <div v-loading="countLoading" v-if="countLoading" element-loading-text="正在计算成绩，请稍候..." style="height:100%;" />
    <t-table-view
      v-else
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'score'">
            {{ scope.row.score === null ? '-' : scope.row.score }}
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { querySeasonTeamScorePage, queryMatchSceneInstanceVOPage, calculateScore } from '@/api/match/index.js'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      camp: '',
      promotion: '',
      searchKeyList: [],
      columnsObj: {},
      columnsViewArr: [],
      searchKeyArr: module.searchKeyArr,
      columnsArr: module.columnsArr,
      columnsViewScoreArr: module.columnsViewScoreArr,
      bigMatchSeasonId: this.$route.params.id || '',
      countLoading: false,
      sceneIdList: []
    }
  },
  inject: ['parentVm'],
  created() {
    this.searchKeyList = this.searchKeyArr[1]
    this.columnsObj = this.columnsArr[1]
    this.columnsViewArr = this.columnsViewScoreArr[1]
    this.getSceneInstance()
  },
  methods: {
    getSceneInstance() {
      const params = {
        id: this.$route.params.id,
        pageType: 0
      }
      queryMatchSceneInstanceVOPage(params).then(res => {
        res.data.records.forEach(item => {
          if (item.participateComputeTeam == 1) {
            this.sceneIdList.push(item.sourceSceneId)
          }
        })
        this.countLoading = true
        calculateScore({ matchId: this.$route.params.id, sceneIdList: this.sceneIdList, type: 2, model: 1 }).then(res => {
          this.countLoading = false
          this.getList(this.countLoading)
        }).catch(() => {
          this.countLoading = false
        })
      })
    },
    getList: function(loading) {
      if (Boolean(loading) === false) {
        this.tableLoading = false
      } else {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      const commonData = {
        ...params,
        camp: this.camp,
        promotion: this.promotion,
        bigMatchSeasonId: this.bigMatchSeasonId
      }
      const teamData = Object.assign({
        teamName: '',
        leaderName: '',
        affiliatedUnit: ''
      }, commonData)
      querySeasonTeamScorePage(teamData).then((res) => {
        if (res.code === 0 || res.code === 200) {
          this.tableData = res.data.records
          this.tableTotal = Number(res.data.total)
          this.tableLoading = false
          this.handleSelection()
        }
      }).catch(() => {
        this.tableLoading = false
      })
    },
    changePromotion() {}
  }
}
</script>
<style scoped lang="less">
.operation-title {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
</style>
<style lang="less">
.common-radio-group {
  border-radius: 10px;
  border: none;
  padding: 2px;
  background-color: #F5F6F9;
  .el-radio-button {
    width: 110px;
    .el-radio-button__inner {
      width: 100%;
      background-color: #F5F6F9;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: bold;
      color: #3F3F3F;
    }
  }
  .is-active {
    .el-radio-button__inner {
      background-color: #fff;
      color: var(--color-600);
      border-radius: 8px;
      font-size: 14px;
      border: none;
      font-weight: bold;
    }
  }
  .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    box-shadow: none;
    -webkit-box-shadow: none;
  }
}
</style>
