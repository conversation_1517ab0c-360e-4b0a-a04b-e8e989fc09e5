<template>
  <div class="content-wrap-layout">
    <div class="category">分类
      <el-select v-model="categoryCode" :popper-append-to-body="false" filterable @change="categoryChange">
        <el-option
          v-for="item in categoryArr"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        >
          <el-tooltip
            placement="bottom"
            width="200">
            <div slot="content">{{ item.name }}</div>
            <span>
              {{ item.name }}
            </span>
          </el-tooltip>
        </el-option>
      </el-select>
    </div>
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      :category-code="categoryCode"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
    >
      <action-menu
        slot="action"
        :module-name="moduleName"
        :select-item="selectItem"
        @call="actionHandler"
      />
    </page-table>
  </div>
</template>

<script>
import moduleConf from './config'
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
import { queryCourseCategory } from '@/api/teacher/index.js'

export default {
  // 教师管理
  name: 'ContentLibrary',
  components: {
    pageTable,
    actionMenu
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      categoryArr: [],
      categoryCode: ''
    }
  },
  mounted() {
    this.getCategoryList()
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
      this.$emit('on-select', this.selectItem)
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {},
    categoryChange: function(id) {
      this.categoryCode = id
      this.$nextTick(() => {
        this.$refs['table'].getList(false)
      })
    },
    getCategoryList: function() {
      queryCourseCategory({ id: '' }).then(res => {
        this.categoryArr = [{ name: '全部', id: '' }, ...res.data]
      })
    }
  }
}
</script>

<style scoped lang="scss">
.content-wrap-layout {
  height: 92% !important;
  .category {
    width: 100%;
    padding: 15px;
    border-bottom: 1px solid #dbdde0;
  }
  .el-select {
    margin-left: 10px;
  }
}
/deep/ .el-select-dropdown {
  width: 150px !important;
}
</style>
