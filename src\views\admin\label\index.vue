<template>
  <div class="content-wrap-layout">
    <div v-if="!fold" :style="{'left': `calc(${percent}% - 10px)`}" class="fold-wrap" @click="handleFold(true)"><i class="el-icon-caret-left" /></div>
    <div v-if="fold" :style="{'left': `calc(${percent}% - 10px)`}" class="fold-wrap" @click="handleFold(false)"><i class="el-icon-caret-right" /></div>
    <split-pane ref="split-pane" :min-percent="minPercent" :default-percent="percent" split="vertical" @resize="resize">
      <tree slot="paneL" ref="tree" @changeLabel="changeLabel" />
      <page-table
        slot="paneR"
        ref="table"
        :default-selected-arr="defaultSelectedArr"
        :filter-data="{ 'lableId': labelId }"
        :cache-pattern="true"
        :module-name="moduleName"
        default-selected-key="lableId"
        style="height: 100%; padding-left: 1px;"
        @refresh="refresh"
        @link-event="linkEvent"
        @on-select="tabelSelect"
        @on-current="tabelCurrent"
      >
        <action-menu
          slot="action"
          :module-name="moduleName"
          :label-id="labelId"
          :select-item="selectItem"
          @call="actionHandler"
        />
      </page-table>
    </split-pane>
  </div>
</template>

<script>
import moduleConf from './config'
import pageTable from './table/index'
import actionMenu from './action/index'
import tree from './tree/index'
import splitPane from '@/packages/mixins/split-pane'

export default {
  components: {
    pageTable,
    actionMenu,
    tree
  },
  mixins: [splitPane],
  data() {
    return {
      cache: true,
      labelId: null,
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: []
    }
  },
  methods: {
    // 改变部门
    changeLabel: function(node) {
      this.labelId = node.id
      this.$nextTick(() => {
        this.$refs['table'].getList()
      })
    },
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
          this.$refs['tree'].getLabelTree()
          break
      }
    },
    refresh: function() {}
  }
}
</script>
