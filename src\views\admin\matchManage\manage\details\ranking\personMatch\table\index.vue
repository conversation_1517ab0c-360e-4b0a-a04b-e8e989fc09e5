<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        阵营：
        <el-radio-group v-model="camp" size="small" @input="getList()">
          <el-radio-button label="" >全部</el-radio-button>
          <el-radio-button label="1">红方</el-radio-button>
          <el-radio-button label="2">蓝方</el-radio-button>
        </el-radio-group>
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索姓名"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="userId"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <div slot="empty">
        <span v-if="!scoreStatus">发布成绩后该页面会呈现最终的发布排行榜信息</span>
        <span v-else>暂无数据</span>
      </div>
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'camp'">
            {{ scope.row[item] === 1 ? '红方' : '蓝方' }}
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { bigMatchScoreQueryPage } from '@/api/match/index.js'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'playerName', label: '姓名', master: true, placeholder: '默认搜索姓名' },
        { key: 'affiliatedUnit', label: '所属单位' }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: module.columnsObj,
      columnsViewArr: module.columnsViewArr,
      camp: ''
    }
  },
  inject: ['parentVm'],
  computed: {
    scoreStatus() { // 是否发布了成绩
      return this.data.scoreStatus == 1 || this.data.scoreStatus == 3
    }
  },
  methods: {
    getList: function() {
      this.tableLoading = true
      const params = this.getPostData('page', 'limit')
      params.bigMatchId = this.$route.params.id
      params.type = 1
      params.status = 1
      params.camp = this.camp
      bigMatchScoreQueryPage(params).then(res => {
        this.tableLoading = false
        this.tableData = res.data.records
        this.tableTotal = res.data.total
      })
    },
    changeGroup() {}
  }
}
</script>
