<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        1. 取消战队成绩会将该战队当前获得的成绩以及战队下所有成员当前的成绩全部清零；清零后无法找回，请谨慎操作。<br>
        2. 取消战队成绩不会影响战队下成员的个人成绩；若需取消个人成绩，请在个人成绩页面中操作取消成绩。</div>
    </el-alert>
    <batch-template
      :data="data"
      :available-data="data"
      :show-delete-warning="false"
      post-key="status"
      view-key="teamName"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import batchTemplate from '@/packages/batch-delete/modal-bat-template'
import modalMixins from '@/packages/mixins/modal_form'
import { cancelSeasonTeamScore } from '@/api/match/index'

export default {
  name: 'Cancel',
  components: { batchTemplate },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: false,
      checked: false
    }
  },
  computed: {
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const result = this.data.map(item => {
        return item.teamId
      })
      const params = {
        bigMatchSeasonId: this.$route.params.id,
        teamIdList: result
      }
      // 批量调用接口
      cancelSeasonTeamScore(params).then(res => {
        this.$message.success('成绩取消成功')
        this.$emit('call', 'refresh')
        this.close()
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>
