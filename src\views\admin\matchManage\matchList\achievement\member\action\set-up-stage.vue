<template>
  <div v-loading="loading" element-loading-text="正在计算成绩，请稍候..." class="dialog-wrap set-up-stage" style="padding:0;">
    <div v-if="combination" style="padding: 20px 20px 0 20px">
      <el-alert :closable="false" type="warning">
        <div slot="title">
          <p>检测到当前比赛为AWD三合一组合赛，即包括一个AWD混战、一个AWD抢占、一个AWD对抗共三个阶段。个人成绩计算方式可选：</p >
        </div>
      </el-alert>
      <div class="mt-10 mb-10">
        <el-radio v-model="model" :label="2" class="mb-5">按公式计算</el-radio>
        <div class="stage-content">个人成绩 = 战队成绩 / 100 * 50% + AWD抢占阶段个人成绩 / 10 * 50%</div>
        <el-radio v-model="model" :label="1">阶段个人成绩之和</el-radio>
      </div>
    </div>
    <div v-loading="tableLoading" v-if="!combination || (combination && model == 1)" style="padding-left: 23px; margin-top: -5px;" class="dialog-wrap-content">
      <el-form ref="form" :model="form" :rules="rules" label-position="left" label-width="0px">
        <el-row :gutter="15">
          <el-col :span="11" :offset="2">阶段名称</el-col>
          <el-col :span="11">期望满分</el-col>
        </el-row>
        <el-row v-for="(item, index) in form.sceneInstanceList" :key="index" :gutter="15">
          <el-col :span="2">
            <el-form-item>
              <el-checkbox v-model="item.checked" />
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item>
              <el-input v-model.trim="tableData[index].sceneName" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item>
              <el-input-number
                v-model.trim="item.scoreSystemPlayer"
                :disabled="!item.checked"
                :step="1" :min="0"
                :controls="false"
                type="number"
                style="width: 85%"
              />
              <el-tooltip transfer>
                <i class="el-icon-question mr-5" style="font-size: 16px;" />
                <div slot="content" class="score-tooltip">
                  <div>红方满分：{{ item.redPlayerMaxScore || 0 }}</div>
                  <div>蓝方满分：{{ item.bluePlayerMaxScore || 0 }}</div>
                </div>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="dialog-footer" style="margin:0;">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!form.sceneInstanceList.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { queryMatchSceneInstanceVOPage, calculateScore, setCalculateRule, queryBigMatchById } from '@/api/match/index.js'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      tableLoading: true,
      loading: false,
      viewKey: '',
      postKey: '',
      id: this.$route.params.id || '',
      tableData: [],
      form: {
        sceneInstanceList: []
      },
      rules: {
        'proportionPerson': [
          { type: 'number', min: 0, max: 100, message: '输入范围：0-100', trigger: 'blur' }
        ]
      },
      typeList: [],
      model: 0
    }
  },
  computed: {
    combination() {
      return this.typeList.length == 3 && this.typeList.filter(item => { return item.type == 'AWDORDINARY' }).length == 1 && this.typeList.filter(item => { return item.type == 'AWDBATTLE' }).length == 1 && this.typeList.filter(item => { return item.type == 'AWDSEIZE' }).length == 1
    },
    proportionTotal() {
      let num = 0
      this.form.sceneInstanceList.forEach(item => {
        if (item.checked && item.proportionPerson) {
          num = num + item.proportionPerson
        }
      })
      return num
    }
  },
  created() {
    this.getData()
    this.geInfo()
  },
  methods: {
    close() {
      this.$emit('close')
    },
    'geInfo': function() {
      queryBigMatchById({ id: this.$route.params.id }).then(res => {
        this.model = res.data.computeWay
      })
    },
    getData() {
      const params = {
        id: this.id,
        pageType: 0
      }
      queryMatchSceneInstanceVOPage(params).then(res => {
        this.tableLoading = false
        this.tableData = res.data.records
        this.tableData.forEach(item => {
          this.form.sceneInstanceList.push({
            'checked': item.participateComputePlayer == 1,
            'redPlayerMaxScore': item.redPlayerMaxScore,
            'bluePlayerMaxScore': item.bluePlayerMaxScore,
            'sceneInstanceId': item.sourceSceneId,
            'scoreSystemPlayer': (item.scoreSystemPlayer || item.scoreSystemPlayer == 0) ? item.scoreSystemPlayer : undefined,
            'proportionPerson': item.proportionPerson
          })
          this.typeList.push({
            type: item.matchType + item.matchModel,
            sourceSceneId: item.sourceSceneId
          })
        })
      }).catch(() => {
        this.tableLoading = false
      })
    },
    confirm: function() {
      this.loading = true
      const sceneInstanceList = this.form.sceneInstanceList.filter(item => item.checked).map(item => {
        return {
          'sceneInstanceId': item.sceneInstanceId,
          'scoreSystemPlayer': (item.scoreSystemPlayer || item.scoreSystemPlayer == 0) ? item.scoreSystemPlayer : null,
          'proportionPerson': item.proportionPerson
        }
      })
      const params = {
        matchId: this.id,
        sceneInstanceList: sceneInstanceList,
        type: 1,
        model: this.model
      }
      setCalculateRule(params).then(r => {
        calculateScore(params).then(res => {
          this.$emit('call', 'refresh')
          this.loading = false
          this.close()
        }).catch(() => {
          this.loading = false
        })
      })
    }
  }
}
</script>
<style lang="less" scoped>
  .set-up-stage {
    ::v-deep {
      .el-checkbox__input.is-checked + .el-checkbox__label {
        color: #333;
      }
      .is-disabled {
        .el-checkbox__input.is-checked + .el-checkbox__label {
          color: #C0C4CC !important;
        }
      }
    }
    .stage-content {
      background: #eee;
      padding: 5px;
      margin: 0 0 10px 22px;
    }
  }
  .dialog-wrap-content {
    max-height: 400px;
    overflow: auto;
    padding: 20px;
  }
</style>
