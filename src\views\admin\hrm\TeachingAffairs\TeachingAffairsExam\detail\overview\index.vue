<template>
  <el-row :gutter="20">
    <el-col :span="12">
      <detail-card title="基本信息">
        <el-form slot="content" ref="form" :model="formData" label-position="left" label-width="150px">
          <el-form-item label="考试名称:">
            <div>{{ formData.examName }}</div>
          </el-form-item>
          <!-- <el-form-item label="证书名称:">
            <div>{{ formData.credentialName }}</div>
          </el-form-item>
          <el-form-item label="证书等级:">
            <div>{{ level }}</div>
          </el-form-item> -->
          <el-form-item label="考试试卷:">
            <el-tag
              v-if="formData.selectedExamPaper"
              :disable-transitions="true"
            >
              {{ formData.selectedExamPaper }}
            </el-tag>
          </el-form-item>
          <el-form-item label="考试时间:">
            <div>{{ formData.date1 }}</div>
          </el-form-item>
          <el-form-item label="考试时长:" >
            <div>{{ formData.time }}</div>
          </el-form-item>
          <el-form-item label="考试须知:">
            <div v-html="formData.notice"/>
          </el-form-item>
          <el-form-item label="备注:">
            <div>{{ formData.description }}</div>
          </el-form-item>
        </el-form>
      </detail-card>
    </el-col>
    <el-col :span="12"/>
  </el-row>
</template>

<script>
import module from './config.js'
import detailCard from '@/packages/detail-view/detail-card.vue'
import { queryById } from '@/api/exam/index.js'

export default {
  name: module.name,
  components: {
    detailCard
  },
  mixins: [],
  data() {
    return {
      loading: false,
      formData: {
        examName: '',
        credentialName: '',
        level: '',
        selectedExamPaper: '',
        date1: '',
        time: '',
        notice: '',
        description: ''
      },
      examLevel: [
        { value: 1, label: '初级' },
        { value: 2, label: '中级' },
        { value: 3, label: '高级' }
      ],
      level: ''
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.params.hasOwnProperty('schedulingCode') ? to.params.schedulingCode : null
      const fromId = from.params.hasOwnProperty('schedulingCode') ? from.params.schedulingCode : null
      if (toId !== fromId) {
        this.getEaxmById()
      }
    }
  },
  created() {
    this.getEaxmById()
  },
  methods: {
    getEaxmById() {
      const id = Number(this.$route.params.id)
      queryById({ id: id }).then((res) => {
        if (res.code === 0) {
          this.formData.examName = res.data.name
          this.formData.credentialName = res.data.certificateName
          this.formData.level = res.data.certificateLevel
          this.formData.selectedExamPaper = res.data.paperName
          this.formData.date1 = res.data.beginTime.split(' ')[0] + ' ' + res.data.beginTime.split(' ')[1]
          this.formData.time = res.data.distanceTime
          this.formData.notice = res.data.description
          this.formData.description = res.data.mark
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.notice {
  display: inline-block;
  max-height: 300px;
  overflow: auto;
}
</style>
